<template>
  <div class="settings-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>系统设置</h2>
        <p>系统配置与管理</p>
      </div>
    </div>

    <!-- 设置导航 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="settings-nav">
          <el-menu
            v-model:default-active="activeTab"
            class="settings-menu"
            @select="handleTabChange"
          >
            <el-menu-item index="basic">
              <el-icon><Setting /></el-icon>
              <span>基础设置</span>
            </el-menu-item>
            <el-menu-item index="users">
              <el-icon><User /></el-icon>
              <span>用户管理</span>
            </el-menu-item>
            <el-menu-item index="roles">
              <el-icon><UserFilled /></el-icon>
              <span>角色权限</span>
            </el-menu-item>
            <el-menu-item index="system">
              <el-icon><Monitor /></el-icon>
              <span>系统配置</span>
            </el-menu-item>
            <el-menu-item index="backup">
              <el-icon><FolderOpened /></el-icon>
              <span>数据备份</span>
            </el-menu-item>
            <el-menu-item index="logs">
              <el-icon><Document /></el-icon>
              <span>系统日志</span>
            </el-menu-item>
          </el-menu>
        </el-card>
      </el-col>

      <el-col :span="18">
        <!-- 基础设置 -->
        <div v-if="activeTab === 'basic'" class="settings-content">
          <el-card>
            <template #header>
              <span>基础设置</span>
            </template>
            
            <el-form
              ref="basicFormRef"
              :model="basicForm"
              :rules="basicRules"
              label-width="120px"
            >
              <el-form-item label="系统名称" prop="systemName">
                <el-input v-model="basicForm.systemName" placeholder="请输入系统名称" />
              </el-form-item>
              
              <el-form-item label="系统Logo">
                <el-upload
                  class="logo-uploader"
                  action="#"
                  :show-file-list="false"
                  :on-success="handleLogoSuccess"
                  :before-upload="beforeLogoUpload"
                >
                  <img v-if="basicForm.logoUrl" :src="basicForm.logoUrl" class="logo" />
                  <el-icon v-else class="logo-uploader-icon"><Plus /></el-icon>
                </el-upload>
              </el-form-item>
              
              <el-form-item label="公司名称" prop="companyName">
                <el-input v-model="basicForm.companyName" placeholder="请输入公司名称" />
              </el-form-item>
              
              <el-form-item label="联系电话" prop="phone">
                <el-input v-model="basicForm.phone" placeholder="请输入联系电话" />
              </el-form-item>
              
              <el-form-item label="联系邮箱" prop="email">
                <el-input v-model="basicForm.email" placeholder="请输入联系邮箱" />
              </el-form-item>
              
              <el-form-item label="公司地址">
                <el-input
                  v-model="basicForm.address"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入公司地址"
                />
              </el-form-item>
              
              <el-form-item label="系统描述">
                <el-input
                  v-model="basicForm.description"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入系统描述"
                />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="saveBasicSettings" :loading="saving">
                  保存设置
                </el-button>
                <el-button @click="resetBasicForm">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>

        <!-- 用户管理 -->
        <div v-if="activeTab === 'users'" class="settings-content">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>用户管理</span>
                <el-button type="primary" @click="showAddUserDialog = true">
                  <el-icon><Plus /></el-icon>
                  新增用户
                </el-button>
              </div>
            </template>
            
            <!-- 用户搜索 -->
            <el-form :model="userSearchForm" inline class="search-form">
              <el-form-item label="用户名">
                <el-input
                  v-model="userSearchForm.username"
                  placeholder="请输入用户名"
                  clearable
                  @keyup.enter="searchUsers"
                />
              </el-form-item>
              <el-form-item label="角色">
                <el-select v-model="userSearchForm.role" placeholder="请选择角色" clearable>
                  <el-option label="管理员" value="admin" />
                  <el-option label="代理人" value="agent" />
                  <el-option label="客服" value="service" />
                  <el-option label="财务" value="finance" />
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="userSearchForm.status" placeholder="请选择状态" clearable>
                  <el-option label="启用" value="active" />
                  <el-option label="禁用" value="inactive" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="searchUsers">搜索</el-button>
                <el-button @click="resetUserSearch">重置</el-button>
              </el-form-item>
            </el-form>
            
            <!-- 用户列表 -->
            <el-table v-loading="loading" :data="userList" stripe>
              <el-table-column prop="username" label="用户名" width="120" />
              <el-table-column prop="realName" label="真实姓名" width="120" />
              <el-table-column prop="email" label="邮箱" width="200" />
              <el-table-column prop="phone" label="电话" width="130" />
              <el-table-column prop="role" label="角色" width="100">
                <template #default="{ row }">
                  <el-tag :type="getRoleColor(row.role)">{{ getRoleText(row.role) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template #default="{ row }">
                  <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                    {{ row.status === 'active' ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="lastLogin" label="最后登录" width="150" />
              <el-table-column prop="createTime" label="创建时间" width="150" />
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" type="text" @click="editUser(row)">
                    编辑
                  </el-button>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleUserStatus(row)"
                    :class="row.status === 'active' ? 'danger' : 'success'"
                  >
                    {{ row.status === 'active' ? '禁用' : '启用' }}
                  </el-button>
                  <el-button size="small" type="text" @click="resetPassword(row)">
                    重置密码
                  </el-button>
                  <el-button size="small" type="text" class="danger" @click="deleteUser(row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="userPagination.page"
                v-model:page-size="userPagination.size"
                :total="userTotal"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleUserSizeChange"
                @current-change="handleUserPageChange"
              />
            </div>
          </el-card>
        </div>

        <!-- 角色权限 -->
        <div v-if="activeTab === 'roles'" class="settings-content">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>角色权限管理</span>
                <el-button type="primary" @click="showAddRoleDialog = true">
                  <el-icon><Plus /></el-icon>
                  新增角色
                </el-button>
              </div>
            </template>
            
            <el-table :data="roleList" stripe>
              <el-table-column prop="name" label="角色名称" width="150" />
              <el-table-column prop="code" label="角色代码" width="150" />
              <el-table-column prop="description" label="角色描述" />
              <el-table-column prop="userCount" label="用户数量" width="100" align="center" />
              <el-table-column prop="createTime" label="创建时间" width="150" />
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" type="text" @click="editRole(row)">
                    编辑
                  </el-button>
                  <el-button size="small" type="text" @click="setPermissions(row)">
                    权限设置
                  </el-button>
                  <el-button
                    size="small"
                    type="text"
                    class="danger"
                    @click="deleteRole(row)"
                    :disabled="row.code === 'admin'"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>

        <!-- 系统配置 -->
        <div v-if="activeTab === 'system'" class="settings-content">
          <el-card>
            <template #header>
              <span>系统配置</span>
            </template>
            
            <el-form
              ref="systemFormRef"
              :model="systemForm"
              label-width="150px"
            >
              <el-divider content-position="left">安全设置</el-divider>
              
              <el-form-item label="密码最小长度">
                <el-input-number
                  v-model="systemForm.passwordMinLength"
                  :min="6"
                  :max="20"
                  controls-position="right"
                />
              </el-form-item>
              
              <el-form-item label="密码复杂度">
                <el-checkbox-group v-model="systemForm.passwordComplexity">
                  <el-checkbox label="uppercase">包含大写字母</el-checkbox>
                  <el-checkbox label="lowercase">包含小写字母</el-checkbox>
                  <el-checkbox label="number">包含数字</el-checkbox>
                  <el-checkbox label="special">包含特殊字符</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              
              <el-form-item label="登录失败锁定">
                <el-switch v-model="systemForm.loginLockEnabled" />
              </el-form-item>
              
              <el-form-item label="最大失败次数" v-if="systemForm.loginLockEnabled">
                <el-input-number
                  v-model="systemForm.maxLoginAttempts"
                  :min="3"
                  :max="10"
                  controls-position="right"
                />
              </el-form-item>
              
              <el-form-item label="锁定时间(分钟)" v-if="systemForm.loginLockEnabled">
                <el-input-number
                  v-model="systemForm.lockDuration"
                  :min="5"
                  :max="60"
                  controls-position="right"
                />
              </el-form-item>
              
              <el-divider content-position="left">会话设置</el-divider>
              
              <el-form-item label="会话超时(分钟)">
                <el-input-number
                  v-model="systemForm.sessionTimeout"
                  :min="10"
                  :max="480"
                  controls-position="right"
                />
              </el-form-item>
              
              <el-form-item label="记住登录">
                <el-switch v-model="systemForm.rememberLoginEnabled" />
              </el-form-item>
              
              <el-divider content-position="left">文件设置</el-divider>
              
              <el-form-item label="文件上传大小(MB)">
                <el-input-number
                  v-model="systemForm.maxFileSize"
                  :min="1"
                  :max="100"
                  controls-position="right"
                />
              </el-form-item>
              
              <el-form-item label="允许的文件类型">
                <el-select
                  v-model="systemForm.allowedFileTypes"
                  multiple
                  placeholder="请选择允许的文件类型"
                  style="width: 100%"
                >
                  <el-option label="PDF" value="pdf" />
                  <el-option label="Word" value="doc,docx" />
                  <el-option label="Excel" value="xls,xlsx" />
                  <el-option label="PowerPoint" value="ppt,pptx" />
                  <el-option label="图片" value="jpg,jpeg,png,gif" />
                  <el-option label="文本" value="txt" />
                </el-select>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="saveSystemSettings" :loading="saving">
                  保存配置
                </el-button>
                <el-button @click="resetSystemForm">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>

        <!-- 数据备份 -->
        <div v-if="activeTab === 'backup'" class="settings-content">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>数据备份</span>
                <el-button type="primary" @click="createBackup" :loading="backupLoading">
                  <el-icon><FolderAdd /></el-icon>
                  创建备份
                </el-button>
              </div>
            </template>
            
            <!-- 备份设置 -->
            <el-form :model="backupForm" inline class="backup-form">
              <el-form-item label="自动备份">
                <el-switch v-model="backupForm.autoBackup" @change="toggleAutoBackup" />
              </el-form-item>
              <el-form-item label="备份频率" v-if="backupForm.autoBackup">
                <el-select v-model="backupForm.frequency">
                  <el-option label="每日" value="daily" />
                  <el-option label="每周" value="weekly" />
                  <el-option label="每月" value="monthly" />
                </el-select>
              </el-form-item>
              <el-form-item label="保留天数" v-if="backupForm.autoBackup">
                <el-input-number
                  v-model="backupForm.retentionDays"
                  :min="7"
                  :max="365"
                  controls-position="right"
                />
              </el-form-item>
            </el-form>
            
            <!-- 备份列表 -->
            <el-table :data="backupList" stripe>
              <el-table-column prop="name" label="备份名称" />
              <el-table-column prop="size" label="文件大小" width="120" />
              <el-table-column prop="type" label="备份类型" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.type === 'auto' ? 'primary' : 'success'">
                    {{ row.type === 'auto' ? '自动' : '手动' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间" width="150" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getBackupStatusColor(row.status)">
                    {{ getBackupStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" type="text" @click="downloadBackup(row)">
                    下载
                  </el-button>
                  <el-button size="small" type="text" @click="restoreBackup(row)">
                    恢复
                  </el-button>
                  <el-button size="small" type="text" class="danger" @click="deleteBackup(row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>

        <!-- 系统日志 -->
        <div v-if="activeTab === 'logs'" class="settings-content">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>系统日志</span>
                <el-button @click="clearLogs">
                  <el-icon><Delete /></el-icon>
                  清空日志
                </el-button>
              </div>
            </template>
            
            <!-- 日志筛选 -->
            <el-form :model="logSearchForm" inline class="search-form">
              <el-form-item label="日志级别">
                <el-select v-model="logSearchForm.level" placeholder="请选择级别" clearable>
                  <el-option label="错误" value="error" />
                  <el-option label="警告" value="warning" />
                  <el-option label="信息" value="info" />
                  <el-option label="调试" value="debug" />
                </el-select>
              </el-form-item>
              <el-form-item label="操作类型">
                <el-select v-model="logSearchForm.action" placeholder="请选择操作" clearable>
                  <el-option label="登录" value="login" />
                  <el-option label="登出" value="logout" />
                  <el-option label="创建" value="create" />
                  <el-option label="更新" value="update" />
                  <el-option label="删除" value="delete" />
                </el-select>
              </el-form-item>
              <el-form-item label="时间范围">
                <el-date-picker
                  v-model="logSearchForm.dateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="searchLogs">搜索</el-button>
                <el-button @click="resetLogSearch">重置</el-button>
              </el-form-item>
            </el-form>
            
            <!-- 日志列表 -->
            <el-table :data="logList" stripe>
              <el-table-column prop="level" label="级别" width="80">
                <template #default="{ row }">
                  <el-tag :type="getLogLevelColor(row.level)" size="small">
                    {{ row.level.toUpperCase() }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="action" label="操作" width="100" />
              <el-table-column prop="user" label="用户" width="120" />
              <el-table-column prop="ip" label="IP地址" width="130" />
              <el-table-column prop="message" label="日志内容" min-width="300" />
              <el-table-column prop="createTime" label="时间" width="150" />
            </el-table>
            
            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="logPagination.page"
                v-model:page-size="logPagination.size"
                :total="logTotal"
                :page-sizes="[20, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleLogSizeChange"
                @current-change="handleLogPageChange"
              />
            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>

    <!-- 新增用户对话框 -->
    <el-dialog
      v-model="showAddUserDialog"
      :title="editingUser ? '编辑用户' : '新增用户'"
      width="600px"
      @close="resetUserForm"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" :disabled="editingUser" />
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="userForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        
        <el-form-item label="电话" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入电话" />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色" style="width: 100%">
            <el-option label="管理员" value="admin" />
            <el-option label="代理人" value="agent" />
            <el-option label="客服" value="service" />
            <el-option label="财务" value="finance" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="密码" prop="password" v-if="!editingUser">
          <el-input v-model="userForm.password" type="password" placeholder="请输入密码" show-password />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword" v-if="!editingUser">
          <el-input v-model="userForm.confirmPassword" type="password" placeholder="请确认密码" show-password />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-radio-group v-model="userForm.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddUserDialog = false">取消</el-button>
        <el-button type="primary" @click="saveUser" :loading="saving">
          {{ editingUser ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 新增角色对话框 -->
    <el-dialog
      v-model="showAddRoleDialog"
      :title="editingRole ? '编辑角色' : '新增角色'"
      width="500px"
      @close="resetRoleForm"
    >
      <el-form
        ref="roleFormRef"
        :model="roleForm"
        :rules="roleRules"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleForm.name" placeholder="请输入角色名称" />
        </el-form-item>
        
        <el-form-item label="角色代码" prop="code">
          <el-input v-model="roleForm.code" placeholder="请输入角色代码" :disabled="editingRole" />
        </el-form-item>
        
        <el-form-item label="角色描述">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddRoleDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRole" :loading="saving">
          {{ editingRole ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting, User, UserFilled, Monitor, FolderOpened, Document,
  Plus, FolderAdd, Delete
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const backupLoading = ref(false)
const activeTab = ref('basic')
const showAddUserDialog = ref(false)
const showAddRoleDialog = ref(false)
const editingUser = ref(null)
const editingRole = ref(null)

// 表单引用
const basicFormRef = ref()
const systemFormRef = ref()
const userFormRef = ref()
const roleFormRef = ref()

// 基础设置表单
const basicForm = reactive({
  systemName: '智邦知识产权数字化系统',
  logoUrl: '',
  companyName: '智邦知识产权代理有限公司',
  phone: '************',
  email: '<EMAIL>',
  address: '北京市朝阳区建国门外大街1号',
  description: '专业的知识产权管理系统，为企业提供全方位的知识产权服务。'
})

const basicRules = {
  systemName: [
    { required: true, message: '请输入系统名称', trigger: 'blur' }
  ],
  companyName: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入联系邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 系统配置表单
const systemForm = reactive({
  passwordMinLength: 8,
  passwordComplexity: ['lowercase', 'number'],
  loginLockEnabled: true,
  maxLoginAttempts: 5,
  lockDuration: 30,
  sessionTimeout: 120,
  rememberLoginEnabled: true,
  maxFileSize: 10,
  allowedFileTypes: ['pdf', 'doc,docx', 'xls,xlsx', 'jpg,jpeg,png,gif']
})

// 用户管理
const userSearchForm = reactive({
  username: '',
  role: '',
  status: ''
})

const userPagination = reactive({
  page: 1,
  size: 10
})

const userTotal = ref(0)

const userList = ref([
  {
    id: 1,
    username: 'admin',
    realName: '系统管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    role: 'admin',
    status: 'active',
    lastLogin: '2024-01-25 10:30:00',
    createTime: '2024-01-01 00:00:00'
  },
  {
    id: 2,
    username: 'zhangdaili',
    realName: '张代理',
    email: '<EMAIL>',
    phone: '13800138001',
    role: 'agent',
    status: 'active',
    lastLogin: '2024-01-25 09:15:00',
    createTime: '2024-01-02 00:00:00'
  },
  {
    id: 3,
    username: 'lidaili',
    realName: '李代理',
    email: '<EMAIL>',
    phone: '13800138002',
    role: 'agent',
    status: 'active',
    lastLogin: '2024-01-24 16:45:00',
    createTime: '2024-01-03 00:00:00'
  }
])

const userForm = reactive({
  username: '',
  realName: '',
  email: '',
  phone: '',
  role: '',
  password: '',
  confirmPassword: '',
  status: 'active'
})

const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== userForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 角色管理
const roleList = ref([
  {
    id: 1,
    name: '系统管理员',
    code: 'admin',
    description: '系统最高权限管理员',
    userCount: 1,
    createTime: '2024-01-01 00:00:00'
  },
  {
    id: 2,
    name: '代理人',
    code: 'agent',
    description: '知识产权代理人',
    userCount: 5,
    createTime: '2024-01-01 00:00:00'
  },
  {
    id: 3,
    name: '客服',
    code: 'service',
    description: '客户服务人员',
    userCount: 3,
    createTime: '2024-01-01 00:00:00'
  },
  {
    id: 4,
    name: '财务',
    code: 'finance',
    description: '财务管理人员',
    userCount: 2,
    createTime: '2024-01-01 00:00:00'
  }
])

const roleForm = reactive({
  name: '',
  code: '',
  description: ''
})

const roleRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色代码', trigger: 'blur' }
  ]
}

// 备份管理
const backupForm = reactive({
  autoBackup: false,
  frequency: 'daily',
  retentionDays: 30
})

const backupList = ref([
  {
    id: 1,
    name: 'backup_2024_01_25_10_30_00.sql',
    size: '125.6 MB',
    type: 'manual',
    createTime: '2024-01-25 10:30:00',
    status: 'completed'
  },
  {
    id: 2,
    name: 'backup_2024_01_24_02_00_00.sql',
    size: '123.2 MB',
    type: 'auto',
    createTime: '2024-01-24 02:00:00',
    status: 'completed'
  },
  {
    id: 3,
    name: 'backup_2024_01_23_02_00_00.sql',
    size: '121.8 MB',
    type: 'auto',
    createTime: '2024-01-23 02:00:00',
    status: 'completed'
  }
])

// 日志管理
const logSearchForm = reactive({
  level: '',
  action: '',
  dateRange: []
})

const logPagination = reactive({
  page: 1,
  size: 20
})

const logTotal = ref(0)

const logList = ref([
  {
    id: 1,
    level: 'info',
    action: '登录',
    user: 'admin',
    ip: '*************',
    message: '用户登录成功',
    createTime: '2024-01-25 10:30:00'
  },
  {
    id: 2,
    level: 'warning',
    action: '登录',
    user: 'zhangdaili',
    ip: '*************',
    message: '用户登录失败，密码错误',
    createTime: '2024-01-25 10:25:00'
  },
  {
    id: 3,
    level: 'info',
    action: '创建',
    user: 'admin',
    ip: '*************',
    message: '创建新用户：lidaili',
    createTime: '2024-01-25 10:20:00'
  }
])

// 方法
const handleTabChange = (key) => {
  activeTab.value = key
}

const handleLogoSuccess = (response, file) => {
  basicForm.logoUrl = URL.createObjectURL(file.raw)
}

const beforeLogoUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

const saveBasicSettings = async () => {
  if (!basicFormRef.value) return
  
  try {
    await basicFormRef.value.validate()
    saving.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('基础设置保存成功')
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const resetBasicForm = () => {
  if (basicFormRef.value) {
    basicFormRef.value.resetFields()
  }
}

const saveSystemSettings = async () => {
  saving.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('系统配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const resetSystemForm = () => {
  if (systemFormRef.value) {
    systemFormRef.value.resetFields()
  }
}

// 用户管理方法
const searchUsers = () => {
  userPagination.page = 1
  loadUsers()
}

const resetUserSearch = () => {
  Object.assign(userSearchForm, {
    username: '',
    role: '',
    status: ''
  })
  searchUsers()
}

const loadUsers = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    userTotal.value = userList.value.length
  } catch (error) {
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

const handleUserSizeChange = (size) => {
  userPagination.size = size
  loadUsers()
}

const handleUserPageChange = (page) => {
  userPagination.page = page
  loadUsers()
}

const editUser = (user) => {
  editingUser.value = user
  Object.assign(userForm, {
    username: user.username,
    realName: user.realName,
    email: user.email,
    phone: user.phone,
    role: user.role,
    status: user.status
  })
  showAddUserDialog.value = true
}

const toggleUserStatus = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确认${user.status === 'active' ? '禁用' : '启用'}用户"${user.realName}"？`,
      '确认操作',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    user.status = user.status === 'active' ? 'inactive' : 'active'
    ElMessage.success(`用户${user.status === 'active' ? '启用' : '禁用'}成功`)
  } catch (error) {
    // 用户取消操作
  }
}

const resetPassword = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确认重置用户"${user.realName}"的密码？`,
      '确认重置',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('密码重置成功，新密码已发送至用户邮箱')
  } catch (error) {
    // 用户取消操作
  }
}

const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确认删除用户"${user.realName}"？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = userList.value.findIndex(item => item.id === user.id)
    if (index > -1) {
      userList.value.splice(index, 1)
      userTotal.value--
      ElMessage.success('删除成功')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const saveUser = async () => {
  if (!userFormRef.value) return
  
  try {
    await userFormRef.value.validate()
    saving.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingUser.value) {
      // 更新用户
      Object.assign(editingUser.value, userForm)
      ElMessage.success('用户更新成功')
    } else {
      // 新增用户
      const newUser = {
        id: Date.now(),
        ...userForm,
        lastLogin: '-',
        createTime: new Date().toLocaleString('zh-CN')
      }
      userList.value.unshift(newUser)
      userTotal.value++
      ElMessage.success('用户创建成功')
    }
    
    showAddUserDialog.value = false
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const resetUserForm = () => {
  editingUser.value = null
  Object.assign(userForm, {
    username: '',
    realName: '',
    email: '',
    phone: '',
    role: '',
    password: '',
    confirmPassword: '',
    status: 'active'
  })
  if (userFormRef.value) {
    userFormRef.value.clearValidate()
  }
}

// 角色管理方法
const editRole = (role) => {
  editingRole.value = role
  Object.assign(roleForm, {
    name: role.name,
    code: role.code,
    description: role.description
  })
  showAddRoleDialog.value = true
}

const setPermissions = (role) => {
  ElMessage.info('权限设置功能开发中...')
}

const deleteRole = async (role) => {
  try {
    await ElMessageBox.confirm(
      `确认删除角色"${role.name}"？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = roleList.value.findIndex(item => item.id === role.id)
    if (index > -1) {
      roleList.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const saveRole = async () => {
  if (!roleFormRef.value) return
  
  try {
    await roleFormRef.value.validate()
    saving.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingRole.value) {
      // 更新角色
      Object.assign(editingRole.value, roleForm)
      ElMessage.success('角色更新成功')
    } else {
      // 新增角色
      const newRole = {
        id: Date.now(),
        ...roleForm,
        userCount: 0,
        createTime: new Date().toLocaleString('zh-CN')
      }
      roleList.value.unshift(newRole)
      ElMessage.success('角色创建成功')
    }
    
    showAddRoleDialog.value = false
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const resetRoleForm = () => {
  editingRole.value = null
  Object.assign(roleForm, {
    name: '',
    code: '',
    description: ''
  })
  if (roleFormRef.value) {
    roleFormRef.value.clearValidate()
  }
}

// 备份管理方法
const toggleAutoBackup = (value) => {
  ElMessage.success(`自动备份已${value ? '启用' : '禁用'}`)
}

const createBackup = async () => {
  backupLoading.value = true
  try {
    // 模拟备份过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    const newBackup = {
      id: Date.now(),
      name: `backup_${new Date().toISOString().replace(/[:.]/g, '_').slice(0, -5)}.sql`,
      size: '126.8 MB',
      type: 'manual',
      createTime: new Date().toLocaleString('zh-CN'),
      status: 'completed'
    }
    
    backupList.value.unshift(newBackup)
    ElMessage.success('备份创建成功')
  } catch (error) {
    ElMessage.error('备份创建失败')
  } finally {
    backupLoading.value = false
  }
}

const downloadBackup = (backup) => {
  ElMessage.success(`开始下载备份：${backup.name}`)
}

const restoreBackup = async (backup) => {
  try {
    await ElMessageBox.confirm(
      `确认恢复备份"${backup.name}"？此操作将覆盖当前数据！`,
      '确认恢复',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('备份恢复成功')
  } catch (error) {
    // 用户取消操作
  }
}

const deleteBackup = async (backup) => {
  try {
    await ElMessageBox.confirm(
      `确认删除备份"${backup.name}"？`,
      '确认删除',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = backupList.value.findIndex(item => item.id === backup.id)
    if (index > -1) {
      backupList.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch (error) {
    // 用户取消删除
  }
}

// 日志管理方法
const searchLogs = () => {
  logPagination.page = 1
  loadLogs()
}

const resetLogSearch = () => {
  Object.assign(logSearchForm, {
    level: '',
    action: '',
    dateRange: []
  })
  searchLogs()
}

const loadLogs = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    logTotal.value = logList.value.length
  } catch (error) {
    ElMessage.error('加载日志列表失败')
  } finally {
    loading.value = false
  }
}

const handleLogSizeChange = (size) => {
  logPagination.size = size
  loadLogs()
}

const handleLogPageChange = (page) => {
  logPagination.page = page
  loadLogs()
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确认清空所有日志？此操作不可恢复！',
      '确认清空',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    logList.value = []
    logTotal.value = 0
    ElMessage.success('日志清空成功')
  } catch (error) {
    // 用户取消操作
  }
}

// 辅助方法
const getRoleText = (role) => {
  const roleMap = {
    admin: '管理员',
    agent: '代理人',
    service: '客服',
    finance: '财务'
  }
  return roleMap[role] || role
}

const getRoleColor = (role) => {
  const colorMap = {
    admin: 'danger',
    agent: 'primary',
    service: 'success',
    finance: 'warning'
  }
  return colorMap[role] || 'info'
}

const getBackupStatusText = (status) => {
  const statusMap = {
    completed: '已完成',
    running: '进行中',
    failed: '失败'
  }
  return statusMap[status] || status
}

const getBackupStatusColor = (status) => {
  const colorMap = {
    completed: 'success',
    running: 'primary',
    failed: 'danger'
  }
  return colorMap[status] || 'info'
}

const getLogLevelColor = (level) => {
  const colorMap = {
    error: 'danger',
    warning: 'warning',
    info: 'primary',
    debug: 'info'
  }
  return colorMap[level] || 'info'
}

// 组件挂载
onMounted(() => {
  loadUsers()
  loadLogs()
})
</script>

<style lang="scss" scoped>
.settings-page {
  .page-header {
    margin-bottom: 20px;
    
    .header-left {
      h2 {
        margin: 0 0 4px 0;
        color: #303133;
      }
      
      p {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .settings-nav {
    .settings-menu {
      border: none;
      
      .el-menu-item {
        border-radius: 6px;
        margin-bottom: 4px;
        
        &.is-active {
          background-color: #ecf5ff;
          color: #409eff;
        }
      }
    }
  }
  
  .settings-content {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .search-form {
      margin-bottom: 20px;
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 6px;
    }
    
    .backup-form {
      margin-bottom: 20px;
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 6px;
    }
    
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
  
  .logo-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;
      
      &:hover {
        border-color: #409eff;
      }
    }
    
    .logo-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 120px;
      height: 120px;
      line-height: 120px;
      text-align: center;
    }
    
    .logo {
      width: 120px;
      height: 120px;
      display: block;
      object-fit: cover;
    }
  }
}

// 按钮颜色样式
.el-button.danger {
  color: #f56c6c;
  
  &:hover {
    color: #f78989;
  }
}

.el-button.success {
  color: #67c23a;
  
  &:hover {
    color: #85ce61;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .settings-page {
    .el-row {
      flex-direction: column;
    }
    
    .settings-nav {
      margin-bottom: 20px;
      
      .settings-menu {
        display: flex;
        overflow-x: auto;
        
        .el-menu-item {
          white-space: nowrap;
        }
      }
    }
    
    .search-form {
      :deep(.el-form--inline) {
        .el-form-item {
          display: block;
          margin-right: 0;
          margin-bottom: 16px;
        }
      }
    }
  }
}
</style>