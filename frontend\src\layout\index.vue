<template>
  <div class="app-wrapper" :class="{ 'sidebar-collapsed': isCollapsed }">
    <!-- 侧边栏 -->
    <div class="sidebar-container">
      <div class="sidebar-logo">
        <el-icon v-if="isCollapsed" size="24" color="#409eff">
          <Document />
        </el-icon>
        <template v-else>
          <el-icon size="24" color="#409eff">
            <Document />
          </el-icon>
          <span class="logo-text">智邦IP系统</span>
        </template>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapsed"
        :unique-opened="true"
        router
        class="sidebar-menu"
      >
        <el-menu-item index="/dashboard">
          <el-icon><Odometer /></el-icon>
          <template #title>仪表盘</template>
        </el-menu-item>
        
        <el-sub-menu index="crm">
          <template #title>
            <el-icon><User /></el-icon>
            <span>客户管理</span>
          </template>
          <el-menu-item index="/crm/leads">线索管理</el-menu-item>
          <el-menu-item index="/crm/customers">客户档案</el-menu-item>
          <el-menu-item index="/crm/opportunities">商机看板</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="cases">
          <template #title>
            <el-icon><Folder /></el-icon>
            <span>案件管理</span>
          </template>
          <el-menu-item index="/cases/list">案件列表</el-menu-item>
          <el-menu-item index="/cases/workflow">流程管理</el-menu-item>
          <el-menu-item index="/cases/documents">文档管理</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="search">
          <template #title>
            <el-icon><Search /></el-icon>
            <span>检索分析</span>
          </template>
          <el-menu-item index="/search/patent">专利检索</el-menu-item>
          <el-menu-item index="/search/trademark">商标检索</el-menu-item>
          <el-menu-item index="/search/analysis">分析报告</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="finance">
          <template #title>
            <el-icon><Money /></el-icon>
            <span>财务管理</span>
          </template>
          <el-menu-item index="/finance/fees">费用管理</el-menu-item>
          <el-menu-item index="/finance/billing">账单结算</el-menu-item>
          <el-menu-item index="/finance/renewal">年费维护</el-menu-item>
        </el-sub-menu>
        
        <el-menu-item index="/knowledge">
          <el-icon><Reading /></el-icon>
          <template #title>知识库</template>
        </el-menu-item>
        
        <el-menu-item index="/reports">
          <el-icon><DataAnalysis /></el-icon>
          <template #title>统计报表</template>
        </el-menu-item>
        
        <el-sub-menu index="settings" v-if="userStore.hasPermission('admin')">
          <template #title>
            <el-icon><Setting /></el-icon>
            <span>系统设置</span>
          </template>
          <el-menu-item index="/settings/users">用户管理</el-menu-item>
          <el-menu-item index="/settings/roles">角色权限</el-menu-item>
          <el-menu-item index="/settings/system">系统配置</el-menu-item>
        </el-sub-menu>
      </el-menu>
    </div>
    
    <!-- 主内容区 -->
    <div class="main-container">
      <!-- 顶部导航 -->
      <div class="navbar">
        <div class="navbar-left">
          <el-button
            type="text"
            @click="toggleSidebar"
            class="sidebar-toggle"
          >
            <el-icon size="18">
              <Expand v-if="isCollapsed" />
              <Fold v-else />
            </el-icon>
          </el-button>
          
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item
              v-for="item in breadcrumbList"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="navbar-right">
          <!-- 通知 -->
          <el-badge :value="notificationCount" class="navbar-item">
            <el-button type="text" @click="showNotifications">
              <el-icon size="18"><Bell /></el-icon>
            </el-button>
          </el-badge>
          
          <!-- 全屏 -->
          <el-button type="text" @click="toggleFullscreen" class="navbar-item">
            <el-icon size="18">
              <FullScreen v-if="!isFullscreen" />
              <Aim v-else />
            </el-icon>
          </el-button>
          
          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserCommand" class="user-dropdown">
            <div class="user-info">
              <el-avatar :size="32" :src="userStore.userInfo.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ userStore.userInfo.name || userStore.userInfo.realName || '用户' }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  个人设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 页面内容 -->
      <div class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </div>
    
    <!-- 通知抽屉 -->
    <el-drawer
      v-model="notificationDrawer"
      title="通知中心"
      direction="rtl"
      size="400px"
    >
      <div class="notification-list">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="notification-item"
          :class="{ 'unread': !notification.read }"
        >
          <div class="notification-icon">
            <el-icon :color="getNotificationColor(notification.type)">
              <component :is="getNotificationIcon(notification.type)" />
            </el-icon>
          </div>
          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-message">{{ notification.message }}</div>
            <div class="notification-time">{{ formatTime(notification.time) }}</div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document, Odometer, User, Folder, Search, Money, Reading,
  DataAnalysis, Setting, Expand, Fold, Bell, FullScreen,
  Aim, ArrowDown, SwitchButton, InfoFilled, WarningFilled,
  SuccessFilled, CircleCloseFilled
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const isCollapsed = ref(false)
const isFullscreen = ref(false)
const notificationDrawer = ref(false)
const notificationCount = ref(5)

// 模拟通知数据
const notifications = ref([
  {
    id: 1,
    type: 'info',
    title: '案件提醒',
    message: '专利申请案件 ZL202301234567 即将到期',
    time: new Date(Date.now() - 1000 * 60 * 30),
    read: false
  },
  {
    id: 2,
    type: 'warning',
    title: '年费提醒',
    message: '商标注册证第12345678号年费即将到期',
    time: new Date(Date.now() - 1000 * 60 * 60 * 2),
    read: false
  },
  {
    id: 3,
    type: 'success',
    title: '审查通过',
    message: '发明专利申请已通过实质审查',
    time: new Date(Date.now() - 1000 * 60 * 60 * 24),
    read: true
  }
])

// 计算属性
const activeMenu = computed(() => {
  const { path } = route
  return path
})

const breadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map(item => ({
    title: item.meta.title,
    path: item.path
  }))
})

// 方法
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

const showNotifications = () => {
  notificationDrawer.value = true
}

const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings/profile')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        await userStore.logoutAction()
        ElMessage.success('退出登录成功')
      } catch (error) {
        // 用户取消
      }
      break
  }
}

const getNotificationIcon = (type) => {
  const iconMap = {
    info: InfoFilled,
    warning: WarningFilled,
    success: SuccessFilled,
    error: CircleCloseFilled
  }
  return iconMap[type] || InfoFilled
}

const getNotificationColor = (type) => {
  const colorMap = {
    info: '#409eff',
    warning: '#e6a23c',
    success: '#67c23a',
    error: '#f56c6c'
  }
  return colorMap[type] || '#409eff'
}

const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return `${days}天前`
  }
}

// 监听全屏状态变化
watch(() => document.fullscreenElement, (val) => {
  isFullscreen.value = !!val
})

// 组件挂载
onMounted(() => {
  // 监听全屏事件
  document.addEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement
  })
})
</script>

<style lang="scss" scoped>
.app-wrapper {
  display: flex;
  height: 100vh;
  width: 100%;
}

.sidebar-container {
  width: $sidebar-width;
  height: 100%;
  background: #304156;
  transition: width 0.3s;
  overflow: hidden;
  
  .sidebar-logo {
    height: $header-height;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #2b3a4b;
    color: white;
    
    .logo-text {
      margin-left: 8px;
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .sidebar-menu {
    border: none;
    background: #304156;
    
    :deep(.el-menu-item) {
      color: #bfcbd9;
      
      &:hover {
        background-color: #263445;
        color: #409eff;
      }
      
      &.is-active {
        background-color: #409eff;
        color: white;
      }
    }
    
    :deep(.el-sub-menu__title) {
      color: #bfcbd9;
      
      &:hover {
        background-color: #263445;
        color: #409eff;
      }
    }
  }
}

.sidebar-collapsed {
  .sidebar-container {
    width: $sidebar-collapsed-width;
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.navbar {
  height: $header-height;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .navbar-left {
    display: flex;
    align-items: center;
    
    .sidebar-toggle {
      margin-right: 20px;
      color: #5a5e66;
      
      &:hover {
        color: #409eff;
      }
    }
    
    .breadcrumb {
      font-size: 14px;
    }
  }
  
  .navbar-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .navbar-item {
      color: #5a5e66;
      
      &:hover {
        color: #409eff;
      }
    }
    
    .user-dropdown {
      .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 8px;
        border-radius: 4px;
        transition: background-color 0.3s;
        
        &:hover {
          background-color: #f5f7fa;
        }
        
        .username {
          margin: 0 8px;
          font-size: 14px;
          color: #303133;
        }
        
        .dropdown-icon {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}

.app-main {
  flex: 1;
  overflow: auto;
  background: #f0f2f5;
  padding: 20px;
}

.notification-list {
  .notification-item {
    display: flex;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    
    &.unread {
      background-color: #f0f9ff;
    }
    
    .notification-icon {
      margin-right: 12px;
      flex-shrink: 0;
    }
    
    .notification-content {
      flex: 1;
      
      .notification-title {
        font-weight: 600;
        margin-bottom: 4px;
        color: #303133;
      }
      
      .notification-message {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
        line-height: 1.4;
      }
      
      .notification-time {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

// 页面切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

// 响应式设计
@media (max-width: 768px) {
  .app-wrapper {
    .sidebar-container {
      position: fixed;
      z-index: 1000;
      left: -$sidebar-width;
      transition: left 0.3s;
      
      &.mobile-open {
        left: 0;
      }
    }
    
    .main-container {
      margin-left: 0;
    }
  }
}
</style>