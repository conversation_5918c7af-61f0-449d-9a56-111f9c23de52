<template>
  <div class="case-list-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>案件管理</h2>
        <p>管理专利、商标等知识产权案件</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          新建案件
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="stat in statsData" :key="stat.key">
          <el-card class="stat-card" :class="stat.type">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="24">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="案件编号">
          <el-input
            v-model="searchForm.caseNumber"
            placeholder="请输入案件编号"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="案件名称">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入案件名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="案件类型">
          <el-select
            v-model="searchForm.type"
            placeholder="请选择案件类型"
            clearable
            style="width: 150px"
          >
            <el-option label="发明专利" value="invention" />
            <el-option label="实用新型" value="utility" />
            <el-option label="外观设计" value="design" />
            <el-option label="商标注册" value="trademark" />
          </el-select>
        </el-form-item>
        <el-form-item label="案件状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择案件状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待提交" value="draft" />
            <el-option label="审查中" value="examining" />
            <el-option label="已授权" value="granted" />
            <el-option label="已驳回" value="rejected" />
            <el-option label="已撤回" value="withdrawn" />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人">
          <el-select
            v-model="searchForm.agent"
            placeholder="请选择负责人"
            clearable
            style="width: 120px"
          >
            <el-option label="张代理" value="zhang" />
            <el-option label="李代理" value="li" />
            <el-option label="王代理" value="wang" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 案件列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>案件列表 ({{ total }})</span>
          <div class="header-actions">
            <el-button size="small" @click="batchOperation">
              <el-icon><Operation /></el-icon>
              批量操作
            </el-button>
            <el-button size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="caseList"
        stripe
        @selection-change="handleSelectionChange"
        @row-click="viewCase"
        style="cursor: pointer"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="caseNumber" label="案件编号" width="140" fixed="left">
          <template #default="{ row }">
            <el-link type="primary" @click.stop="viewCase(row)">
              {{ row.caseNumber }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="案件名称" min-width="200">
          <template #default="{ row }">
            <div class="case-title">
              <div class="title">{{ row.title }}</div>
              <div class="client">{{ row.client }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)" size="small">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="agent" label="负责人" width="100" />
        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="{ row }">
            <el-tag :type="getPriorityColor(row.priority)" size="small">
              {{ row.priority }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="deadline" label="截止日期" width="120">
          <template #default="{ row }">
            <span :class="getDeadlineClass(row.deadline)">
              {{ formatDate(row.deadline) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度" width="120">
          <template #default="{ row }">
            <el-progress
              :percentage="row.progress"
              :color="getProgressColor(row.progress)"
              :stroke-width="6"
            />
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="金额" width="100" align="right">
          <template #default="{ row }">
            <span class="amount">¥{{ formatAmount(row.amount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="text" @click.stop="viewCase(row)">
              查看
            </el-button>
            <el-button size="small" type="text" @click.stop="editCase(row)">
              编辑
            </el-button>
            <el-button size="small" type="text" @click.stop="viewDocuments(row)">
              文档
            </el-button>
            <el-dropdown @command="(cmd) => handleMoreAction(cmd, row)" @click.stop>
              <el-button size="small" type="text">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="timeline">时间线</el-dropdown-item>
                  <el-dropdown-item command="tasks">任务管理</el-dropdown-item>
                  <el-dropdown-item command="fees">费用记录</el-dropdown-item>
                  <el-dropdown-item command="contacts">联系记录</el-dropdown-item>
                  <el-dropdown-item divided command="archive">
                    归档案件
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 新建案件对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="新建案件"
      width="900px"
      @close="resetForm"
    >
      <el-form
        ref="caseFormRef"
        :model="caseForm"
        :rules="caseRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="案件名称" prop="title">
              <el-input v-model="caseForm.title" placeholder="请输入案件名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="案件类型" prop="type">
              <el-select v-model="caseForm.type" placeholder="请选择案件类型" style="width: 100%">
                <el-option label="发明专利" value="invention" />
                <el-option label="实用新型" value="utility" />
                <el-option label="外观设计" value="design" />
                <el-option label="商标注册" value="trademark" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户" prop="client">
              <el-select
                v-model="caseForm.client"
                placeholder="请选择客户"
                filterable
                style="width: 100%"
              >
                <el-option label="北京科技有限公司" value="北京科技有限公司" />
                <el-option label="上海创新科技" value="上海创新科技" />
                <el-option label="王发明" value="王发明" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="agent">
              <el-select v-model="caseForm.agent" placeholder="请选择负责人" style="width: 100%">
                <el-option label="张代理" value="张代理" />
                <el-option label="李代理" value="李代理" />
                <el-option label="王代理" value="王代理" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="caseForm.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option label="高" value="高" />
                <el-option label="中" value="中" />
                <el-option label="低" value="低" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止日期" prop="deadline">
              <el-date-picker
                v-model="caseForm.deadline"
                type="date"
                placeholder="请选择截止日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预估金额">
              <el-input-number
                v-model="caseForm.amount"
                :min="0"
                :precision="2"
                placeholder="请输入预估金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="案件状态">
              <el-select v-model="caseForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="待提交" value="draft" />
                <el-option label="审查中" value="examining" />
                <el-option label="已授权" value="granted" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="案件描述">
          <el-input
            v-model="caseForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入案件描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCase" :loading="saving">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Download, Operation, ArrowDown,
  Folder, Clock, Check, Warning
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showAddDialog = ref(false)
const caseFormRef = ref()
const selectedCases = ref([])
const total = ref(0)

// 统计数据
const statsData = reactive([
  {
    key: 'total',
    label: '总案件',
    value: '1,234',
    icon: Folder,
    type: 'primary'
  },
  {
    key: 'examining',
    label: '审查中',
    value: '456',
    icon: Clock,
    type: 'warning'
  },
  {
    key: 'granted',
    label: '已授权',
    value: '678',
    icon: Check,
    type: 'success'
  },
  {
    key: 'urgent',
    label: '紧急',
    value: '23',
    icon: Warning,
    type: 'danger'
  }
])

// 搜索表单
const searchForm = reactive({
  caseNumber: '',
  title: '',
  type: '',
  status: '',
  agent: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20
})

// 案件表单
const caseForm = reactive({
  title: '',
  type: '',
  client: '',
  agent: '',
  priority: '中',
  deadline: '',
  amount: 0,
  status: 'draft',
  description: ''
})

// 表单验证规则
const caseRules = {
  title: [
    { required: true, message: '请输入案件名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择案件类型', trigger: 'change' }
  ],
  client: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  agent: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ],
  deadline: [
    { required: true, message: '请选择截止日期', trigger: 'change' }
  ]
}

// 案件列表数据
const caseList = ref([
  {
    id: 1,
    caseNumber: 'ZL202301001',
    title: '一种新型智能手机充电器',
    client: '北京科技有限公司',
    type: 'invention',
    status: 'examining',
    agent: '张代理',
    priority: '高',
    deadline: new Date('2024-03-15'),
    progress: 65,
    amount: 15000,
    createTime: new Date('2023-01-15')
  },
  {
    id: 2,
    caseNumber: 'ZL202301002',
    title: '智能家居控制系统',
    client: '上海创新科技',
    type: 'utility',
    status: 'granted',
    agent: '李代理',
    priority: '中',
    deadline: new Date('2024-02-20'),
    progress: 100,
    amount: 8000,
    createTime: new Date('2023-02-10')
  },
  {
    id: 3,
    caseNumber: 'TM202301003',
    title: '创新科技商标',
    client: '王发明',
    type: 'trademark',
    status: 'draft',
    agent: '王代理',
    priority: '低',
    deadline: new Date('2024-04-10'),
    progress: 25,
    amount: 3000,
    createTime: new Date('2023-03-05')
  }
])

// 方法
const handleSearch = () => {
  pagination.page = 1
  loadCases()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    caseNumber: '',
    title: '',
    type: '',
    status: '',
    agent: ''
  })
  handleSearch()
}

const loadCases = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    total.value = caseList.value.length
  } catch (error) {
    ElMessage.error('加载案件列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedCases.value = selection
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadCases()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadCases()
}

const viewCase = (caseItem) => {
  router.push(`/cases/${caseItem.id}`)
}

const editCase = (caseItem) => {
  router.push(`/cases/${caseItem.id}/edit`)
}

const viewDocuments = (caseItem) => {
  router.push(`/cases/${caseItem.id}/documents`)
}

const handleMoreAction = (command, caseItem) => {
  switch (command) {
    case 'timeline':
      router.push(`/cases/${caseItem.id}/timeline`)
      break
    case 'tasks':
      router.push(`/cases/${caseItem.id}/tasks`)
      break
    case 'fees':
      router.push(`/cases/${caseItem.id}/fees`)
      break
    case 'contacts':
      router.push(`/cases/${caseItem.id}/contacts`)
      break
    case 'archive':
      ElMessage.info('归档功能开发中...')
      break
  }
}

const batchOperation = () => {
  if (selectedCases.value.length === 0) {
    ElMessage.warning('请先选择要操作的案件')
    return
  }
  ElMessage.info('批量操作功能开发中...')
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const saveCase = async () => {
  if (!caseFormRef.value) return
  
  try {
    await caseFormRef.value.validate()
    saving.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('案件创建成功')
    showAddDialog.value = false
    loadCases()
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  Object.assign(caseForm, {
    title: '',
    type: '',
    client: '',
    agent: '',
    priority: '中',
    deadline: '',
    amount: 0,
    status: 'draft',
    description: ''
  })
  if (caseFormRef.value) {
    caseFormRef.value.clearValidate()
  }
}

// 辅助方法
const getTypeText = (type) => {
  const typeMap = {
    invention: '发明',
    utility: '实用',
    design: '外观',
    trademark: '商标'
  }
  return typeMap[type] || type
}

const getTypeColor = (type) => {
  const colorMap = {
    invention: 'primary',
    utility: 'success',
    design: 'warning',
    trademark: 'info'
  }
  return colorMap[type] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    draft: '待提交',
    examining: '审查中',
    granted: '已授权',
    rejected: '已驳回',
    withdrawn: '已撤回'
  }
  return statusMap[status] || status
}

const getStatusColor = (status) => {
  const colorMap = {
    draft: 'info',
    examining: 'warning',
    granted: 'success',
    rejected: 'danger',
    withdrawn: 'info'
  }
  return colorMap[status] || 'info'
}

const getPriorityColor = (priority) => {
  const colorMap = {
    '高': 'danger',
    '中': 'warning',
    '低': 'info'
  }
  return colorMap[priority] || 'info'
}

const getDeadlineClass = (deadline) => {
  const now = new Date()
  const diff = deadline - now
  const days = Math.ceil(diff / (1000 * 60 * 60 * 24))
  
  if (days < 0) return 'overdue'
  if (days <= 7) return 'urgent'
  if (days <= 30) return 'warning'
  return 'normal'
}

const getProgressColor = (progress) => {
  if (progress >= 80) return '#67c23a'
  if (progress >= 50) return '#e6a23c'
  return '#f56c6c'
}

const formatAmount = (amount) => {
  return amount.toLocaleString()
}

const formatDate = (date) => {
  return date.toLocaleDateString('zh-CN')
}

// 组件挂载
onMounted(() => {
  loadCases()
})
</script>

<style lang="scss" scoped>
.case-list-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      h2 {
        margin: 0 0 4px 0;
        color: #303133;
      }
      
      p {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .stats-section {
    margin-bottom: 20px;
    
    .stat-card {
      height: 80px;
      
      &.primary {
        border-left: 4px solid #409eff;
      }
      
      &.warning {
        border-left: 4px solid #e6a23c;
      }
      
      &.success {
        border-left: 4px solid #67c23a;
      }
      
      &.danger {
        border-left: 4px solid #f56c6c;
      }
      
      .stat-content {
        display: flex;
        align-items: center;
        height: 100%;
        
        .stat-icon {
          margin-right: 16px;
          color: #409eff;
        }
        
        .stat-info {
          .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .case-title {
      .title {
        font-weight: 500;
        margin-bottom: 2px;
      }
      
      .client {
        font-size: 12px;
        color: #909399;
      }
    }
    
    .amount {
      font-weight: 600;
      color: #67c23a;
    }
    
    .overdue {
      color: #f56c6c;
      font-weight: 600;
    }
    
    .urgent {
      color: #e6a23c;
      font-weight: 600;
    }
    
    .warning {
      color: #e6a23c;
    }
    
    .normal {
      color: #303133;
    }
    
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .case-list-page {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
    
    .search-card {
      :deep(.el-form--inline) {
        .el-form-item {
          display: block;
          margin-right: 0;
          margin-bottom: 16px;
        }
      }
    }
  }
}
</style>