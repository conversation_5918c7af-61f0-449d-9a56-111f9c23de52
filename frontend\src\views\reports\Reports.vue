<template>
  <div class="reports-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>统计报表</h2>
        <p>数据分析与可视化报表</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="exportReport">
          <el-icon><Download /></el-icon>
          导出报表
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 时间范围选择 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item label="报表类型">
          <el-select v-model="filterForm.reportType" @change="handleTypeChange">
            <el-option label="业务概览" value="overview" />
            <el-option label="案件分析" value="cases" />
            <el-option label="财务分析" value="finance" />
            <el-option label="客户分析" value="customers" />
            <el-option label="代理人效率" value="agents" />
          </el-select>
        </el-form-item>
        <el-form-item label="数据维度">
          <el-select v-model="filterForm.dimension">
            <el-option label="按日" value="day" />
            <el-option label="按周" value="week" />
            <el-option label="按月" value="month" />
            <el-option label="按季度" value="quarter" />
            <el-option label="按年" value="year" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 业务概览报表 -->
    <div v-if="filterForm.reportType === 'overview'" class="overview-section">
      <!-- KPI指标卡片 -->
      <el-row :gutter="20" class="kpi-cards">
        <el-col :xs="12" :sm="6" v-for="kpi in kpiData" :key="kpi.key">
          <el-card class="kpi-card" :class="kpi.trend">
            <div class="kpi-content">
              <div class="kpi-icon">
                <el-icon :size="32">
                  <component :is="kpi.icon" />
                </el-icon>
              </div>
              <div class="kpi-info">
                <div class="kpi-value">{{ kpi.value }}</div>
                <div class="kpi-label">{{ kpi.label }}</div>
                <div class="kpi-change" :class="kpi.trend">
                  <el-icon>
                    <component :is="kpi.trend === 'up' ? 'TrendCharts' : 'Bottom'" />
                  </el-icon>
                  {{ kpi.change }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="20" class="chart-section">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>业务趋势分析</span>
            </template>
            <div ref="trendChartRef" style="height: 300px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>案件类型分布</span>
            </template>
            <div ref="pieChartRef" style="height: 300px"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-section">
        <el-col :span="24">
          <el-card>
            <template #header>
              <span>月度收入对比</span>
            </template>
            <div ref="revenueChartRef" style="height: 400px"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 案件分析报表 -->
    <div v-if="filterForm.reportType === 'cases'" class="cases-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>案件状态分布</span>
            </template>
            <div ref="caseStatusChartRef" style="height: 300px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>案件优先级分析</span>
            </template>
            <div ref="casePriorityChartRef" style="height: 300px"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-section">
        <el-col :span="24">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>案件处理效率统计</span>
                <el-button size="small" @click="exportCaseData">
                  <el-icon><Download /></el-icon>
                  导出数据
                </el-button>
              </div>
            </template>
            <el-table :data="caseEfficiencyData" stripe>
              <el-table-column prop="agent" label="代理人" width="120" />
              <el-table-column prop="totalCases" label="总案件数" width="100" align="center" />
              <el-table-column prop="completedCases" label="已完成" width="100" align="center" />
              <el-table-column prop="pendingCases" label="进行中" width="100" align="center" />
              <el-table-column prop="avgDuration" label="平均处理时长" width="120" align="center" />
              <el-table-column prop="completionRate" label="完成率" width="100" align="center">
                <template #default="{ row }">
                  <el-progress
                    :percentage="row.completionRate"
                    :color="getProgressColor(row.completionRate)"
                    :stroke-width="8"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="efficiency" label="效率评级" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getEfficiencyType(row.efficiency)">{{ row.efficiency }}</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 财务分析报表 -->
    <div v-if="filterForm.reportType === 'finance'" class="finance-section">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="finance-summary">
            <div class="summary-item">
              <div class="summary-label">总收入</div>
              <div class="summary-value income">¥ 2,456,789</div>
              <div class="summary-change up">+12.5%</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="finance-summary">
            <div class="summary-item">
              <div class="summary-label">总支出</div>
              <div class="summary-value expense">¥ 1,234,567</div>
              <div class="summary-change down">-5.2%</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="finance-summary">
            <div class="summary-item">
              <div class="summary-label">净利润</div>
              <div class="summary-value profit">¥ 1,222,222</div>
              <div class="summary-change up">+18.7%</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-section">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>收支趋势对比</span>
            </template>
            <div ref="financeChartRef" style="height: 350px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>费用类型分布</span>
            </template>
            <div ref="expenseChartRef" style="height: 350px"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 客户分析报表 -->
    <div v-if="filterForm.reportType === 'customers'" class="customers-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>客户类型分布</span>
            </template>
            <div ref="customerTypeChartRef" style="height: 300px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>客户活跃度分析</span>
            </template>
            <div ref="customerActivityChartRef" style="height: 300px"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-section">
        <el-col :span="24">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>客户价值排行榜</span>
                <el-button size="small" @click="exportCustomerData">
                  <el-icon><Download /></el-icon>
                  导出数据
                </el-button>
              </div>
            </template>
            <el-table :data="customerValueData" stripe>
              <el-table-column type="index" label="排名" width="80" align="center" />
              <el-table-column prop="name" label="客户名称" width="200" />
              <el-table-column prop="type" label="客户类型" width="120">
                <template #default="{ row }">
                  <el-tag :type="getCustomerTypeColor(row.type)">{{ row.type }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="totalValue" label="总价值" width="120" align="right">
                <template #default="{ row }">
                  ¥ {{ row.totalValue.toLocaleString() }}
                </template>
              </el-table-column>
              <el-table-column prop="caseCount" label="案件数量" width="100" align="center" />
              <el-table-column prop="lastActivity" label="最近活动" width="120" />
              <el-table-column prop="satisfaction" label="满意度" width="120" align="center">
                <template #default="{ row }">
                  <el-rate v-model="row.satisfaction" disabled show-score />
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 代理人效率报表 -->
    <div v-if="filterForm.reportType === 'agents'" class="agents-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card>
            <template #header>
              <span>代理人工作量分布</span>
            </template>
            <div ref="agentWorkloadChartRef" style="height: 400px"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-section">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>代理人效率对比</span>
            </template>
            <div ref="agentEfficiencyChartRef" style="height: 300px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>案件类型专长分析</span>
            </template>
            <div ref="agentSpecialtyChartRef" style="height: 300px"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Download, Refresh, TrendCharts, Bottom
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)

// 筛选表单
const filterForm = reactive({
  dateRange: [],
  reportType: 'overview',
  dimension: 'month'
})

// KPI数据
const kpiData = reactive([
  {
    key: 'totalCases',
    label: '总案件数',
    value: '1,234',
    change: '+12.5%',
    trend: 'up',
    icon: 'Document'
  },
  {
    key: 'completedCases',
    label: '已完成案件',
    value: '987',
    change: '+8.3%',
    trend: 'up',
    icon: 'CircleCheck'
  },
  {
    key: 'totalRevenue',
    label: '总收入',
    value: '¥2.45M',
    change: '+15.2%',
    trend: 'up',
    icon: 'Money'
  },
  {
    key: 'customerSatisfaction',
    label: '客户满意度',
    value: '4.8/5',
    change: '+0.2',
    trend: 'up',
    icon: 'Star'
  }
])

// 案件效率数据
const caseEfficiencyData = ref([
  {
    agent: '张代理',
    totalCases: 45,
    completedCases: 38,
    pendingCases: 7,
    avgDuration: '15天',
    completionRate: 84,
    efficiency: '优秀'
  },
  {
    agent: '李代理',
    totalCases: 52,
    completedCases: 41,
    pendingCases: 11,
    avgDuration: '18天',
    completionRate: 79,
    efficiency: '良好'
  },
  {
    agent: '王代理',
    totalCases: 38,
    completedCases: 35,
    pendingCases: 3,
    avgDuration: '12天',
    completionRate: 92,
    efficiency: '优秀'
  },
  {
    agent: '赵代理',
    totalCases: 41,
    completedCases: 29,
    pendingCases: 12,
    avgDuration: '22天',
    completionRate: 71,
    efficiency: '一般'
  }
])

// 客户价值数据
const customerValueData = ref([
  {
    name: '华为技术有限公司',
    type: '大型企业',
    totalValue: 1250000,
    caseCount: 45,
    lastActivity: '2024-01-25',
    satisfaction: 4.8
  },
  {
    name: '腾讯科技有限公司',
    type: '大型企业',
    totalValue: 980000,
    caseCount: 32,
    lastActivity: '2024-01-23',
    satisfaction: 4.6
  },
  {
    name: '小米科技有限公司',
    type: '中型企业',
    totalValue: 650000,
    caseCount: 28,
    lastActivity: '2024-01-20',
    satisfaction: 4.5
  },
  {
    name: '字节跳动有限公司',
    type: '大型企业',
    totalValue: 580000,
    caseCount: 25,
    lastActivity: '2024-01-18',
    satisfaction: 4.7
  }
])

// 图表引用
const trendChartRef = ref()
const pieChartRef = ref()
const revenueChartRef = ref()
const caseStatusChartRef = ref()
const casePriorityChartRef = ref()
const financeChartRef = ref()
const expenseChartRef = ref()
const customerTypeChartRef = ref()
const customerActivityChartRef = ref()
const agentWorkloadChartRef = ref()
const agentEfficiencyChartRef = ref()
const agentSpecialtyChartRef = ref()

// 图表实例
let trendChart = null
let pieChart = null
let revenueChart = null
let caseStatusChart = null
let casePriorityChart = null
let financeChart = null
let expenseChart = null
let customerTypeChart = null
let customerActivityChart = null
let agentWorkloadChart = null
let agentEfficiencyChart = null
let agentSpecialtyChart = null

// 方法
const handleDateChange = () => {
  refreshData()
}

const handleTypeChange = () => {
  nextTick(() => {
    initCharts()
  })
}

const refreshData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('数据刷新成功')
    initCharts()
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const exportReport = () => {
  ElMessage.success('报表导出中...')
}

const exportCaseData = () => {
  ElMessage.success('案件数据导出中...')
}

const exportCustomerData = () => {
  ElMessage.success('客户数据导出中...')
}

// 辅助方法
const getProgressColor = (percentage) => {
  if (percentage >= 90) return '#67c23a'
  if (percentage >= 70) return '#e6a23c'
  return '#f56c6c'
}

const getEfficiencyType = (efficiency) => {
  const typeMap = {
    '优秀': 'success',
    '良好': 'primary',
    '一般': 'warning',
    '较差': 'danger'
  }
  return typeMap[efficiency] || 'info'
}

const getCustomerTypeColor = (type) => {
  const colorMap = {
    '大型企业': 'danger',
    '中型企业': 'warning',
    '小型企业': 'primary',
    '个人客户': 'info'
  }
  return colorMap[type] || 'info'
}

// 图表初始化
const initCharts = () => {
  if (filterForm.reportType === 'overview') {
    initOverviewCharts()
  } else if (filterForm.reportType === 'cases') {
    initCaseCharts()
  } else if (filterForm.reportType === 'finance') {
    initFinanceCharts()
  } else if (filterForm.reportType === 'customers') {
    initCustomerCharts()
  } else if (filterForm.reportType === 'agents') {
    initAgentCharts()
  }
}

const initOverviewCharts = () => {
  // 业务趋势图
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
    const trendOption = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['新增案件', '完成案件', '收入']
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: [
        {
          type: 'value',
          name: '案件数量'
        },
        {
          type: 'value',
          name: '收入(万元)'
        }
      ],
      series: [
        {
          name: '新增案件',
          type: 'bar',
          data: [45, 52, 38, 65, 48, 72]
        },
        {
          name: '完成案件',
          type: 'bar',
          data: [38, 45, 42, 58, 52, 68]
        },
        {
          name: '收入',
          type: 'line',
          yAxisIndex: 1,
          data: [35, 42, 38, 55, 48, 65]
        }
      ]
    }
    trendChart.setOption(trendOption)
  }

  // 案件类型饼图
  if (pieChartRef.value) {
    pieChart = echarts.init(pieChartRef.value)
    const pieOption = {
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '案件类型',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 335, name: '专利申请' },
            { value: 310, name: '商标注册' },
            { value: 234, name: '版权登记' },
            { value: 135, name: '知识产权诉讼' },
            { value: 148, name: '其他' }
          ]
        }
      ]
    }
    pieChart.setOption(pieOption)
  }

  // 收入对比图
  if (revenueChartRef.value) {
    revenueChart = echarts.init(revenueChartRef.value)
    const revenueOption = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['2023年', '2024年']
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      },
      yAxis: {
        type: 'value',
        name: '收入(万元)'
      },
      series: [
        {
          name: '2023年',
          type: 'line',
          data: [25, 28, 32, 35, 38, 42, 45, 48, 52, 55, 58, 62]
        },
        {
          name: '2024年',
          type: 'line',
          data: [30, 35, 38, 42, 45, 50, 55, 58, 62, 65, 68, 72]
        }
      ]
    }
    revenueChart.setOption(revenueOption)
  }
}

const initCaseCharts = () => {
  // 案件状态分布
  if (caseStatusChartRef.value) {
    caseStatusChart = echarts.init(caseStatusChartRef.value)
    const statusOption = {
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '案件状态',
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 45, name: '已完成' },
            { value: 32, name: '进行中' },
            { value: 18, name: '待审核' },
            { value: 12, name: '已暂停' }
          ]
        }
      ]
    }
    caseStatusChart.setOption(statusOption)
  }

  // 案件优先级分析
  if (casePriorityChartRef.value) {
    casePriorityChart = echarts.init(casePriorityChartRef.value)
    const priorityOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: ['高', '中', '低']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '案件数量',
          type: 'bar',
          data: [25, 68, 45],
          itemStyle: {
            color: function(params) {
              const colors = ['#f56c6c', '#e6a23c', '#67c23a']
              return colors[params.dataIndex]
            }
          }
        }
      ]
    }
    casePriorityChart.setOption(priorityOption)
  }
}

const initFinanceCharts = () => {
  // 收支趋势对比
  if (financeChartRef.value) {
    financeChart = echarts.init(financeChartRef.value)
    const financeOption = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['收入', '支出', '净利润']
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value',
        name: '金额(万元)'
      },
      series: [
        {
          name: '收入',
          type: 'bar',
          data: [45, 52, 48, 65, 58, 72]
        },
        {
          name: '支出',
          type: 'bar',
          data: [25, 28, 32, 35, 38, 42]
        },
        {
          name: '净利润',
          type: 'line',
          data: [20, 24, 16, 30, 20, 30]
        }
      ]
    }
    financeChart.setOption(financeOption)
  }

  // 费用类型分布
  if (expenseChartRef.value) {
    expenseChart = echarts.init(expenseChartRef.value)
    const expenseOption = {
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '费用类型',
          type: 'pie',
          radius: '60%',
          data: [
            { value: 35, name: '人员成本' },
            { value: 25, name: '办公费用' },
            { value: 20, name: '差旅费' },
            { value: 15, name: '培训费' },
            { value: 5, name: '其他' }
          ]
        }
      ]
    }
    expenseChart.setOption(expenseOption)
  }
}

const initCustomerCharts = () => {
  // 客户类型分布
  if (customerTypeChartRef.value) {
    customerTypeChart = echarts.init(customerTypeChartRef.value)
    const typeOption = {
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '客户类型',
          type: 'pie',
          radius: '60%',
          data: [
            { value: 45, name: '大型企业' },
            { value: 35, name: '中型企业' },
            { value: 25, name: '小型企业' },
            { value: 15, name: '个人客户' }
          ]
        }
      ]
    }
    customerTypeChart.setOption(typeOption)
  }

  // 客户活跃度分析
  if (customerActivityChartRef.value) {
    customerActivityChart = echarts.init(customerActivityChartRef.value)
    const activityOption = {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '活跃客户数',
          type: 'line',
          data: [45, 52, 48, 65, 58, 72],
          smooth: true
        }
      ]
    }
    customerActivityChart.setOption(activityOption)
  }
}

const initAgentCharts = () => {
  // 代理人工作量分布
  if (agentWorkloadChartRef.value) {
    agentWorkloadChart = echarts.init(agentWorkloadChartRef.value)
    const workloadOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['专利申请', '商标注册', '版权登记', '诉讼案件']
      },
      xAxis: {
        type: 'category',
        data: ['张代理', '李代理', '王代理', '赵代理', '陈代理']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '专利申请',
          type: 'bar',
          stack: 'total',
          data: [15, 18, 12, 16, 14]
        },
        {
          name: '商标注册',
          type: 'bar',
          stack: 'total',
          data: [12, 15, 10, 13, 11]
        },
        {
          name: '版权登记',
          type: 'bar',
          stack: 'total',
          data: [8, 10, 6, 7, 9]
        },
        {
          name: '诉讼案件',
          type: 'bar',
          stack: 'total',
          data: [5, 6, 4, 3, 7]
        }
      ]
    }
    agentWorkloadChart.setOption(workloadOption)
  }

  // 代理人效率对比
  if (agentEfficiencyChartRef.value) {
    agentEfficiencyChart = echarts.init(agentEfficiencyChartRef.value)
    const efficiencyOption = {
      tooltip: {
        trigger: 'axis'
      },
      radar: {
        indicator: [
          { name: '案件完成率', max: 100 },
          { name: '客户满意度', max: 5 },
          { name: '处理速度', max: 100 },
          { name: '专业能力', max: 100 },
          { name: '沟通能力', max: 100 }
        ]
      },
      series: [
        {
          name: '代理人能力',
          type: 'radar',
          data: [
            {
              value: [84, 4.8, 85, 90, 88],
              name: '张代理'
            },
            {
              value: [79, 4.6, 78, 85, 82],
              name: '李代理'
            }
          ]
        }
      ]
    }
    agentEfficiencyChart.setOption(efficiencyOption)
  }

  // 案件类型专长分析
  if (agentSpecialtyChartRef.value) {
    agentSpecialtyChart = echarts.init(agentSpecialtyChartRef.value)
    const specialtyOption = {
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '专长分布',
          type: 'pie',
          radius: ['30%', '70%'],
          roseType: 'area',
          data: [
            { value: 40, name: '专利申请' },
            { value: 30, name: '商标注册' },
            { value: 20, name: '版权登记' },
            { value: 10, name: '诉讼案件' }
          ]
        }
      ]
    }
    agentSpecialtyChart.setOption(specialtyOption)
  }
}

// 组件挂载
onMounted(() => {
  // 设置默认时间范围为最近30天
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 30)
  
  filterForm.dateRange = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ]
  
  nextTick(() => {
    initCharts()
  })
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (trendChart) trendChart.resize()
    if (pieChart) pieChart.resize()
    if (revenueChart) revenueChart.resize()
    if (caseStatusChart) caseStatusChart.resize()
    if (casePriorityChart) casePriorityChart.resize()
    if (financeChart) financeChart.resize()
    if (expenseChart) expenseChart.resize()
    if (customerTypeChart) customerTypeChart.resize()
    if (customerActivityChart) customerActivityChart.resize()
    if (agentWorkloadChart) agentWorkloadChart.resize()
    if (agentEfficiencyChart) agentEfficiencyChart.resize()
    if (agentSpecialtyChart) agentSpecialtyChart.resize()
  })
})
</script>

<style lang="scss" scoped>
.reports-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      h2 {
        margin: 0 0 4px 0;
        color: #303133;
      }
      
      p {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
    
    .header-right {
      display: flex;
      gap: 12px;
    }
  }
  
  .filter-card {
    margin-bottom: 20px;
  }
  
  .kpi-cards {
    margin-bottom: 20px;
    
    .kpi-card {
      height: 100px;
      
      &.up {
        border-left: 4px solid #67c23a;
      }
      
      &.down {
        border-left: 4px solid #f56c6c;
      }
      
      .kpi-content {
        display: flex;
        align-items: center;
        height: 100%;
        
        .kpi-icon {
          margin-right: 16px;
          color: #409eff;
        }
        
        .kpi-info {
          flex: 1;
          
          .kpi-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .kpi-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 4px;
          }
          
          .kpi-change {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            
            &.up {
              color: #67c23a;
            }
            
            &.down {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }
  
  .chart-section {
    margin-bottom: 20px;
  }
  
  .finance-summary {
    text-align: center;
    
    .summary-item {
      .summary-label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 8px;
      }
      
      .summary-value {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 8px;
        
        &.income {
          color: #67c23a;
        }
        
        &.expense {
          color: #f56c6c;
        }
        
        &.profit {
          color: #409eff;
        }
      }
      
      .summary-change {
        font-size: 14px;
        
        &.up {
          color: #67c23a;
        }
        
        &.down {
          color: #f56c6c;
        }
      }
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .reports-page {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
    
    .filter-card {
      :deep(.el-form--inline) {
        .el-form-item {
          display: block;
          margin-right: 0;
          margin-bottom: 16px;
        }
      }
    }
    
    .kpi-cards {
      .kpi-card {
        margin-bottom: 16px;
      }
    }
  }
}
</style>