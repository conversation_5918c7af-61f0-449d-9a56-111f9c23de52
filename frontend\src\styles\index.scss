// 全局样式文件

// 导入变量
@use './variables.scss' as *;

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: $font-family;
  font-size: $font-size-base;
  color: $text-color-primary;
  background-color: $bg-color;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// 通用类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.full-height {
  height: 100%;
}

.full-width {
  width: 100%;
}

// 边距类
@for $i from 0 through 50 {
  .m-#{$i} {
    margin: #{$i}px;
  }
  .mt-#{$i} {
    margin-top: #{$i}px;
  }
  .mr-#{$i} {
    margin-right: #{$i}px;
  }
  .mb-#{$i} {
    margin-bottom: #{$i}px;
  }
  .ml-#{$i} {
    margin-left: #{$i}px;
  }
  .p-#{$i} {
    padding: #{$i}px;
  }
  .pt-#{$i} {
    padding-top: #{$i}px;
  }
  .pr-#{$i} {
    padding-right: #{$i}px;
  }
  .pb-#{$i} {
    padding-bottom: #{$i}px;
  }
  .pl-#{$i} {
    padding-left: #{$i}px;
  }
}

// 卡片样式
.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

// 页面容器
.page-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

// 表格样式增强
.el-table {
  .el-table__header {
    th {
      background-color: #fafafa;
      color: $text-color-primary;
      font-weight: 600;
    }
  }
}

// 表单样式增强
.form-container {
  .el-form-item__label {
    font-weight: 500;
  }
}

// 按钮组样式
.button-group {
  display: flex;
  gap: 10px;
  
  &.right {
    justify-content: flex-end;
  }
  
  &.center {
    justify-content: center;
  }
}

// 状态标签样式
.status-tag {
  &.success {
    color: #67c23a;
    background-color: #f0f9ff;
    border: 1px solid #67c23a;
  }
  
  &.warning {
    color: #e6a23c;
    background-color: #fdf6ec;
    border: 1px solid #e6a23c;
  }
  
  &.danger {
    color: #f56c6c;
    background-color: #fef0f0;
    border: 1px solid #f56c6c;
  }
  
  &.info {
    color: #909399;
    background-color: #f4f4f5;
    border: 1px solid #909399;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
  
  .card {
    padding: 15px;
  }
  
  .button-group {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
}

// 动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}