<template>
  <div class="patent-search-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>专利检索</h2>
        <p>专业的专利检索与分析工具</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAdvancedSearch = !showAdvancedSearch">
          <el-icon><Search /></el-icon>
          {{ showAdvancedSearch ? '简单检索' : '高级检索' }}
        </el-button>
      </div>
    </div>

    <!-- 检索表单 -->
    <el-card class="search-card">
      <template #header>
        <span>{{ showAdvancedSearch ? '高级检索' : '快速检索' }}</span>
      </template>

      <!-- 简单检索 -->
      <div v-if="!showAdvancedSearch" class="simple-search">
        <el-form :model="simpleForm" inline>
          <el-form-item label="关键词">
            <el-input
              v-model="simpleForm.keyword"
              placeholder="请输入专利名称、摘要或关键词"
              style="width: 400px"
              @keyup.enter="handleSearch"
            >
              <template #append>
                <el-button @click="handleSearch" :loading="searching">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="检索范围">
            <el-select v-model="simpleForm.scope" style="width: 150px">
              <el-option label="全部" value="all" />
              <el-option label="标题" value="title" />
              <el-option label="摘要" value="abstract" />
              <el-option label="权利要求" value="claims" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 高级检索 -->
      <div v-else class="advanced-search">
        <el-form :model="advancedForm" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="专利名称">
                <el-input v-model="advancedForm.title" placeholder="请输入专利名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="申请号">
                <el-input v-model="advancedForm.applicationNumber" placeholder="请输入申请号" />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="申请人">
                <el-input v-model="advancedForm.applicant" placeholder="请输入申请人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发明人">
                <el-input v-model="advancedForm.inventor" placeholder="请输入发明人" />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="IPC分类号">
                <el-input v-model="advancedForm.ipc" placeholder="请输入IPC分类号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="申请日期">
                <el-date-picker
                  v-model="advancedForm.applicationDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="摘要">
            <el-input
              v-model="advancedForm.abstract"
              type="textarea"
              :rows="3"
              placeholder="请输入摘要关键词"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="searching">
              <el-icon><Search /></el-icon>
              开始检索
            </el-button>
            <el-button @click="resetForm">重置</el-button>
            <el-button @click="saveSearchTemplate">保存为模板</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 检索历史 -->
    <el-card v-if="searchHistory.length > 0" class="history-card">
      <template #header>
        <span>检索历史</span>
      </template>
      <div class="history-list">
        <el-tag
          v-for="(item, index) in searchHistory"
          :key="index"
          closable
          @close="removeHistory(index)"
          @click="loadHistory(item)"
          class="history-tag"
        >
          {{ item.keyword || item.title || '高级检索' }}
        </el-tag>
      </div>
    </el-card>

    <!-- 检索结果 -->
    <el-card v-if="searchResults.length > 0" class="results-card">
      <template #header>
        <div class="results-header">
          <span>检索结果 ({{ totalResults }})</span>
          <div class="header-actions">
            <el-button size="small" @click="exportResults">
              <el-icon><Download /></el-icon>
              导出结果
            </el-button>
            <el-button size="small" @click="analyzeResults">
              <el-icon><TrendCharts /></el-icon>
              分析报告
            </el-button>
          </div>
        </div>
      </template>

      <!-- 结果统计 -->
      <div class="result-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ totalResults }}</div>
              <div class="stat-label">检索结果</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ grantedCount }}</div>
              <div class="stat-label">已授权</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ applicationCount }}</div>
              <div class="stat-label">申请中</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ searchTime }}ms</div>
              <div class="stat-label">检索耗时</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 结果列表 -->
      <div class="results-list">
        <div
          v-for="(result, index) in searchResults"
          :key="index"
          class="result-item"
          @click="viewPatentDetail(result)"
        >
          <div class="result-header">
            <div class="result-title">
              <el-link type="primary" @click.stop="viewPatentDetail(result)">
                {{ result.title }}
              </el-link>
              <el-tag :type="getStatusColor(result.status)" size="small">
                {{ result.status }}
              </el-tag>
            </div>
            <div class="result-actions">
              <el-button size="small" text @click.stop="addToCollection(result)">
                <el-icon><Star /></el-icon>
                收藏
              </el-button>
              <el-button size="small" text @click.stop="comparePatent(result)">
                <el-icon><Scale /></el-icon>
                对比
              </el-button>
              <el-button size="small" text @click.stop="downloadPatent(result)">
                <el-icon><Download /></el-icon>
                下载
              </el-button>
            </div>
          </div>
          
          <div class="result-meta">
            <span class="meta-item">
              <strong>申请号:</strong> {{ result.applicationNumber }}
            </span>
            <span class="meta-item">
              <strong>申请人:</strong> {{ result.applicant }}
            </span>
            <span class="meta-item">
              <strong>发明人:</strong> {{ result.inventor }}
            </span>
            <span class="meta-item">
              <strong>申请日:</strong> {{ formatDate(result.applicationDate) }}
            </span>
          </div>
          
          <div class="result-abstract">
            <p>{{ result.abstract }}</p>
          </div>
          
          <div class="result-footer">
            <div class="ipc-classes">
              <el-tag
                v-for="ipc in result.ipcClasses"
                :key="ipc"
                size="small"
                type="info"
              >
                {{ ipc }}
              </el-tag>
            </div>
            <div class="similarity">
              相似度: {{ result.similarity }}%
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="totalResults"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 专利详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="selectedPatent?.title"
      width="80%"
      top="5vh"
    >
      <div v-if="selectedPatent" class="patent-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请号">
            {{ selectedPatent.applicationNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="公开号">
            {{ selectedPatent.publicationNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="申请人">
            {{ selectedPatent.applicant }}
          </el-descriptions-item>
          <el-descriptions-item label="发明人">
            {{ selectedPatent.inventor }}
          </el-descriptions-item>
          <el-descriptions-item label="申请日">
            {{ formatDate(selectedPatent.applicationDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="公开日">
            {{ formatDate(selectedPatent.publicationDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusColor(selectedPatent.status)">
              {{ selectedPatent.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="IPC分类">
            <div>
              <el-tag
                v-for="ipc in selectedPatent.ipcClasses"
                :key="ipc"
                size="small"
                style="margin-right: 4px"
              >
                {{ ipc }}
              </el-tag>
            </div>
          </el-descriptions-item>
        </el-descriptions>
        
        <el-divider content-position="left">摘要</el-divider>
        <p class="abstract-text">{{ selectedPatent.abstract }}</p>
        
        <el-divider content-position="left">权利要求</el-divider>
        <div class="claims-text">
          <p v-for="(claim, index) in selectedPatent.claims" :key="index">
            {{ index + 1 }}. {{ claim }}
          </p>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
        <el-button type="primary" @click="addToCollection(selectedPatent)">
          收藏专利
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Download, TrendCharts, Star, Scale
} from '@element-plus/icons-vue'

// 响应式数据
const searching = ref(false)
const showAdvancedSearch = ref(false)
const showDetailDialog = ref(false)
const selectedPatent = ref(null)
const searchTime = ref(0)

// 简单检索表单
const simpleForm = reactive({
  keyword: '',
  scope: 'all'
})

// 高级检索表单
const advancedForm = reactive({
  title: '',
  applicationNumber: '',
  applicant: '',
  inventor: '',
  ipc: '',
  applicationDate: [],
  abstract: ''
})

// 检索历史
const searchHistory = ref([
  { keyword: '人工智能', scope: 'all' },
  { title: '机器学习', applicant: '百度' },
  { keyword: '区块链技术' }
])

// 分页
const pagination = reactive({
  page: 1,
  size: 20
})

// 检索结果
const searchResults = ref([
  {
    id: 1,
    title: '一种基于人工智能的图像识别方法',
    applicationNumber: 'CN202301234567.8',
    publicationNumber: 'CN116789012A',
    applicant: '北京科技大学',
    inventor: '张三, 李四',
    applicationDate: new Date('2023-05-15'),
    publicationDate: new Date('2023-11-20'),
    status: '已授权',
    abstract: '本发明公开了一种基于人工智能的图像识别方法，通过深度学习算法对图像进行特征提取和分类识别，具有识别准确率高、处理速度快的优点。该方法包括图像预处理、特征提取、模型训练和识别分类四个主要步骤...',
    ipcClasses: ['G06F', 'G06N', 'G06T'],
    similarity: 95,
    claims: [
      '一种基于人工智能的图像识别方法，其特征在于，包括以下步骤：',
      '对输入图像进行预处理，包括尺寸归一化和噪声去除；',
      '使用卷积神经网络提取图像特征；',
      '通过分类器对提取的特征进行分类识别。'
    ]
  },
  {
    id: 2,
    title: '智能语音识别系统及其实现方法',
    applicationNumber: 'CN202301234568.9',
    publicationNumber: 'CN116789013A',
    applicant: '华为技术有限公司',
    inventor: '王五, 赵六',
    applicationDate: new Date('2023-06-10'),
    publicationDate: new Date('2023-12-15'),
    status: '审查中',
    abstract: '本发明涉及语音识别技术领域，公开了一种智能语音识别系统及其实现方法。该系统采用深度神经网络模型，能够准确识别多种语言和方言，支持实时语音转文字功能...',
    ipcClasses: ['G10L', 'G06F', 'G06N'],
    similarity: 88,
    claims: [
      '一种智能语音识别系统，其特征在于，包括：',
      '语音采集模块，用于采集用户语音信号；',
      '预处理模块，用于对语音信号进行降噪和特征提取；',
      '识别模块，使用深度学习模型进行语音识别。'
    ]
  }
])

const totalResults = ref(156)

// 计算属性
const grantedCount = computed(() => {
  return searchResults.value.filter(item => item.status === '已授权').length
})

const applicationCount = computed(() => {
  return searchResults.value.filter(item => item.status === '审查中').length
})

// 方法
const handleSearch = async () => {
  searching.value = true
  const startTime = Date.now()
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 添加到检索历史
    const searchParams = showAdvancedSearch.value ? { ...advancedForm } : { ...simpleForm }
    addToHistory(searchParams)
    
    searchTime.value = Date.now() - startTime
    ElMessage.success(`检索完成，找到 ${totalResults.value} 条结果`)
  } catch (error) {
    ElMessage.error('检索失败，请重试')
  } finally {
    searching.value = false
  }
}

const resetForm = () => {
  if (showAdvancedSearch.value) {
    Object.assign(advancedForm, {
      title: '',
      applicationNumber: '',
      applicant: '',
      inventor: '',
      ipc: '',
      applicationDate: [],
      abstract: ''
    })
  } else {
    Object.assign(simpleForm, {
      keyword: '',
      scope: 'all'
    })
  }
}

const addToHistory = (params) => {
  // 避免重复添加
  const exists = searchHistory.value.some(item => 
    JSON.stringify(item) === JSON.stringify(params)
  )
  
  if (!exists) {
    searchHistory.value.unshift(params)
    // 限制历史记录数量
    if (searchHistory.value.length > 10) {
      searchHistory.value.pop()
    }
  }
}

const removeHistory = (index) => {
  searchHistory.value.splice(index, 1)
}

const loadHistory = (params) => {
  if (params.keyword) {
    showAdvancedSearch.value = false
    Object.assign(simpleForm, params)
  } else {
    showAdvancedSearch.value = true
    Object.assign(advancedForm, params)
  }
}

const saveSearchTemplate = () => {
  ElMessage.info('保存检索模板功能开发中...')
}

const handleSizeChange = (size) => {
  pagination.size = size
  handleSearch()
}

const handlePageChange = (page) => {
  pagination.page = page
  handleSearch()
}

const viewPatentDetail = (patent) => {
  selectedPatent.value = patent
  showDetailDialog.value = true
}

const addToCollection = (patent) => {
  ElMessage.success(`已收藏专利：${patent.title}`)
}

const comparePatent = (patent) => {
  ElMessage.info('专利对比功能开发中...')
}

const downloadPatent = (patent) => {
  ElMessage.info('专利下载功能开发中...')
}

const exportResults = () => {
  ElMessage.info('导出检索结果功能开发中...')
}

const analyzeResults = () => {
  ElMessage.info('分析报告功能开发中...')
}

// 辅助方法
const getStatusColor = (status) => {
  const colorMap = {
    '已授权': 'success',
    '审查中': 'warning',
    '已驳回': 'danger',
    '已撤回': 'info'
  }
  return colorMap[status] || 'info'
}

const formatDate = (date) => {
  return date.toLocaleDateString('zh-CN')
}

// 组件挂载
onMounted(() => {
  // 初始化
})
</script>

<style lang="scss" scoped>
.patent-search-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      h2 {
        margin: 0 0 4px 0;
        color: #303133;
      }
      
      p {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .search-card {
    margin-bottom: 20px;
    
    .simple-search {
      .el-form {
        display: flex;
        align-items: center;
        gap: 20px;
      }
    }
  }
  
  .history-card {
    margin-bottom: 20px;
    
    .history-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .history-tag {
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
  
  .results-card {
    .results-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .result-stats {
      margin-bottom: 20px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      
      .stat-item {
        text-align: center;
        
        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: #409eff;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }
    }
    
    .results-list {
      .result-item {
        border: 1px solid #ebeef5;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 16px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
        }
        
        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;
          
          .result-title {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 12px;
            
            .el-link {
              font-size: 16px;
              font-weight: 500;
            }
          }
          
          .result-actions {
            display: flex;
            gap: 8px;
          }
        }
        
        .result-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          margin-bottom: 12px;
          
          .meta-item {
            font-size: 14px;
            color: #606266;
            
            strong {
              color: #303133;
            }
          }
        }
        
        .result-abstract {
          margin-bottom: 12px;
          
          p {
            margin: 0;
            color: #606266;
            line-height: 1.6;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }
        
        .result-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .ipc-classes {
            display: flex;
            gap: 4px;
          }
          
          .similarity {
            font-size: 14px;
            color: #67c23a;
            font-weight: 500;
          }
        }
      }
    }
    
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
  
  .patent-detail {
    .abstract-text {
      line-height: 1.8;
      color: #606266;
      margin: 16px 0;
    }
    
    .claims-text {
      p {
        margin: 8px 0;
        line-height: 1.6;
        color: #606266;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .patent-search-page {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
    
    .simple-search {
      .el-form {
        flex-direction: column;
        align-items: stretch;
        
        .el-form-item {
          margin-right: 0;
          margin-bottom: 16px;
        }
      }
    }
    
    .result-item {
      .result-header {
        flex-direction: column;
        gap: 12px;
      }
      
      .result-meta {
        flex-direction: column;
        gap: 8px;
      }
      
      .result-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }
    }
  }
}
</style>