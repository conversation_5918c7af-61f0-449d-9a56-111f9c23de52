{"name": "zhibang-ip-system", "version": "1.0.0", "description": "智邦知识产权数字化系统前端", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "element-plus": "^2.3.8", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.4.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.0", "dayjs": "^1.11.9", "nprogress": "^0.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5", "sass": "^1.64.1", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.0.0"}}