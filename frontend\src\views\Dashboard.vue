<template>
  <div class="dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <el-card class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h2>欢迎回来，{{ userStore.user.name }}！</h2>
            <p>今天是 {{ currentDate }}，祝您工作愉快</p>
          </div>
          <div class="welcome-stats">
            <div class="stat-item">
              <div class="stat-number">{{ todayStats.newCases }}</div>
              <div class="stat-label">今日新增案件</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ todayStats.pendingTasks }}</div>
              <div class="stat-label">待处理任务</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ todayStats.expiringSoon }}</div>
              <div class="stat-label">即将到期</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据概览 -->
    <div class="overview-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :lg="6" v-for="item in overviewData" :key="item.key">
          <el-card class="overview-card" :class="item.type">
            <div class="card-content">
              <div class="card-icon">
                <el-icon :size="32">
                  <component :is="item.icon" />
                </el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">{{ item.title }}</div>
                <div class="card-value">{{ item.value }}</div>
                <div class="card-trend" :class="item.trend > 0 ? 'up' : 'down'">
                  <el-icon>
                    <ArrowUp v-if="item.trend > 0" />
                    <ArrowDown v-else />
                  </el-icon>
                  {{ Math.abs(item.trend) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 案件趋势图 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>案件趋势</span>
                <el-select v-model="casesTrendPeriod" size="small" style="width: 100px">
                  <el-option label="7天" value="7d" />
                  <el-option label="30天" value="30d" />
                  <el-option label="90天" value="90d" />
                </el-select>
              </div>
            </template>
            <div ref="casesTrendChart" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 案件类型分布 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>案件类型分布</span>
            </template>
            <div ref="caseTypeChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <!-- 收入统计 -->
        <el-col :xs="24" :lg="16">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>收入统计</span>
                <el-radio-group v-model="revenueType" size="small">
                  <el-radio-button label="monthly">月度</el-radio-button>
                  <el-radio-button label="quarterly">季度</el-radio-button>
                  <el-radio-button label="yearly">年度</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div ref="revenueChart" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 代理人效率 -->
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <span>代理人效率排行</span>
            </template>
            <div class="agent-ranking">
              <div 
                v-for="(agent, index) in agentRanking" 
                :key="agent.id"
                class="ranking-item"
              >
                <div class="ranking-number" :class="`rank-${index + 1}`">
                  {{ index + 1 }}
                </div>
                <el-avatar :size="32" :src="agent.avatar">
                  {{ agent.name.charAt(0) }}
                </el-avatar>
                <div class="agent-info">
                  <div class="agent-name">{{ agent.name }}</div>
                  <div class="agent-cases">{{ agent.cases }}件</div>
                </div>
                <div class="agent-score">{{ agent.score }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快捷操作和待办事项 -->
    <div class="bottom-section">
      <el-row :gutter="20">
        <!-- 快捷操作 -->
        <el-col :xs="24" :lg="8">
          <el-card class="quick-actions-card">
            <template #header>
              <span>快捷操作</span>
            </template>
            <div class="quick-actions">
              <el-button 
                v-for="action in quickActions" 
                :key="action.key"
                :type="action.type"
                :icon="action.icon"
                class="action-button"
                @click="handleQuickAction(action.key)"
              >
                {{ action.label }}
              </el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 待办事项 -->
        <el-col :xs="24" :lg="8">
          <el-card class="todo-card">
            <template #header>
              <div class="card-header">
                <span>待办事项</span>
                <el-badge :value="todoList.filter(item => !item.completed).length" />
              </div>
            </template>
            <div class="todo-list">
              <div 
                v-for="todo in todoList.slice(0, 6)" 
                :key="todo.id"
                class="todo-item"
                :class="{ completed: todo.completed }"
              >
                <el-checkbox 
                  v-model="todo.completed" 
                  @change="updateTodo(todo)"
                />
                <div class="todo-content">
                  <div class="todo-title">{{ todo.title }}</div>
                  <div class="todo-time">{{ formatDate(todo.dueDate) }}</div>
                </div>
                <el-tag 
                  :type="getPriorityType(todo.priority)" 
                  size="small"
                >
                  {{ todo.priority }}
                </el-tag>
              </div>
            </div>
            <div class="todo-footer">
              <el-button type="text" @click="$router.push('/tasks')">
                查看全部
              </el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 最新动态 -->
        <el-col :xs="24" :lg="8">
          <el-card class="activity-card">
            <template #header>
              <span>最新动态</span>
            </template>
            <div class="activity-list">
              <div 
                v-for="activity in recentActivities" 
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-icon" :class="activity.type">
                  <el-icon>
                    <component :is="getActivityIcon(activity.type)" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-desc">{{ activity.description }}</div>
                  <div class="activity-time">{{ formatTime(activity.time) }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  User, Folder, Money, DataAnalysis, ArrowUp, ArrowDown,
  Plus, Search, Edit, Setting, Document, Bell, Clock,
  Check, Warning, InfoFilled
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const casesTrendPeriod = ref('30d')
const revenueType = ref('monthly')
const casesTrendChart = ref()
const caseTypeChart = ref()
const revenueChart = ref()

// 当前日期
const currentDate = new Date().toLocaleDateString('zh-CN', {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  weekday: 'long'
})

// 今日统计
const todayStats = reactive({
  newCases: 12,
  pendingTasks: 8,
  expiringSoon: 3
})

// 概览数据
const overviewData = reactive([
  {
    key: 'totalCases',
    title: '总案件数',
    value: '1,234',
    trend: 12.5,
    icon: Folder,
    type: 'primary'
  },
  {
    key: 'activeClients',
    title: '活跃客户',
    value: '567',
    trend: 8.2,
    icon: User,
    type: 'success'
  },
  {
    key: 'monthlyRevenue',
    title: '月度收入',
    value: '¥89.2万',
    trend: 15.3,
    icon: Money,
    type: 'warning'
  },
  {
    key: 'completionRate',
    title: '完成率',
    value: '94.5%',
    trend: -2.1,
    icon: DataAnalysis,
    type: 'info'
  }
])

// 快捷操作
const quickActions = [
  { key: 'newCase', label: '新建案件', type: 'primary', icon: Plus },
  { key: 'search', label: '专利检索', type: 'success', icon: Search },
  { key: 'newClient', label: '新增客户', type: 'warning', icon: User },
  { key: 'report', label: '生成报告', type: 'info', icon: Document }
]

// 代理人排行
const agentRanking = reactive([
  { id: 1, name: '张代理', cases: 45, score: 98, avatar: '' },
  { id: 2, name: '李代理', cases: 42, score: 95, avatar: '' },
  { id: 3, name: '王代理', cases: 38, score: 92, avatar: '' },
  { id: 4, name: '刘代理', cases: 35, score: 89, avatar: '' },
  { id: 5, name: '陈代理', cases: 32, score: 86, avatar: '' }
])

// 待办事项
const todoList = reactive([
  {
    id: 1,
    title: '审查意见答复',
    dueDate: new Date(Date.now() + 86400000),
    priority: '高',
    completed: false
  },
  {
    id: 2,
    title: '客户会议准备',
    dueDate: new Date(Date.now() + 172800000),
    priority: '中',
    completed: false
  },
  {
    id: 3,
    title: '专利检索报告',
    dueDate: new Date(Date.now() + 259200000),
    priority: '低',
    completed: true
  },
  {
    id: 4,
    title: '年费缴纳提醒',
    dueDate: new Date(Date.now() + 345600000),
    priority: '高',
    completed: false
  }
])

// 最新动态
const recentActivities = reactive([
  {
    id: 1,
    type: 'case',
    title: '新案件创建',
    description: '专利申请案件 ZL202301234567 已创建',
    time: new Date(Date.now() - 1800000)
  },
  {
    id: 2,
    type: 'payment',
    title: '费用收款',
    description: '客户ABC公司支付申请费 ¥3,000',
    time: new Date(Date.now() - 3600000)
  },
  {
    id: 3,
    type: 'deadline',
    title: '期限提醒',
    description: '案件 ZL202201111111 答复期限还有3天',
    time: new Date(Date.now() - 7200000)
  },
  {
    id: 4,
    type: 'approval',
    title: '审查通过',
    description: '发明专利申请通过实质审查',
    time: new Date(Date.now() - 10800000)
  }
])

// 方法
const handleQuickAction = (key) => {
  const actions = {
    newCase: () => router.push('/cases/new'),
    search: () => router.push('/search/patent'),
    newClient: () => router.push('/crm/customers/new'),
    report: () => router.push('/reports')
  }
  
  if (actions[key]) {
    actions[key]()
  } else {
    ElMessage.info(`功能 ${key} 开发中...`)
  }
}

const updateTodo = (todo) => {
  ElMessage.success(todo.completed ? '任务已完成' : '任务已重新打开')
}

const getPriorityType = (priority) => {
  const typeMap = {
    '高': 'danger',
    '中': 'warning',
    '低': 'info'
  }
  return typeMap[priority] || 'info'
}

const getActivityIcon = (type) => {
  const iconMap = {
    case: Document,
    payment: Money,
    deadline: Clock,
    approval: Check,
    warning: Warning,
    info: InfoFilled
  }
  return iconMap[type] || InfoFilled
}

const formatDate = (date) => {
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const hours = Math.floor(diff / (1000 * 60 * 60))
  
  if (hours < 1) {
    const minutes = Math.floor(diff / (1000 * 60))
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    const days = Math.floor(hours / 24)
    return `${days}天前`
  }
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 案件趋势图
    if (casesTrendChart.value) {
      const chart1 = echarts.init(casesTrendChart.value)
      chart1.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value' },
        series: [{
          name: '新增案件',
          type: 'line',
          data: [120, 132, 101, 134, 90, 230],
          smooth: true,
          itemStyle: { color: '#409eff' }
        }]
      })
    }
    
    // 案件类型分布
    if (caseTypeChart.value) {
      const chart2 = echarts.init(caseTypeChart.value)
      chart2.setOption({
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: '60%',
          data: [
            { value: 335, name: '发明专利' },
            { value: 310, name: '实用新型' },
            { value: 234, name: '外观设计' },
            { value: 135, name: '商标注册' }
          ]
        }]
      })
    }
    
    // 收入统计
    if (revenueChart.value) {
      const chart3 = echarts.init(revenueChart.value)
      chart3.setOption({
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value' },
        series: [{
          name: '收入',
          type: 'bar',
          data: [65, 78, 82, 91, 87, 95],
          itemStyle: { color: '#67c23a' }
        }]
      })
    }
  })
}

// 组件挂载
onMounted(() => {
  initCharts()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .welcome-section {
    margin-bottom: 20px;
    
    .welcome-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      
      :deep(.el-card__body) {
        padding: 30px;
      }
      
      .welcome-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .welcome-text {
          h2 {
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 600;
          }
          
          p {
            margin: 0;
            opacity: 0.9;
          }
        }
        
        .welcome-stats {
          display: flex;
          gap: 40px;
          
          .stat-item {
            text-align: center;
            
            .stat-number {
              font-size: 28px;
              font-weight: 700;
              margin-bottom: 4px;
            }
            
            .stat-label {
              font-size: 12px;
              opacity: 0.8;
            }
          }
        }
      }
    }
  }
  
  .overview-section {
    margin-bottom: 20px;
    
    .overview-card {
      height: 120px;
      
      &.primary {
        border-left: 4px solid #409eff;
      }
      
      &.success {
        border-left: 4px solid #67c23a;
      }
      
      &.warning {
        border-left: 4px solid #e6a23c;
      }
      
      &.info {
        border-left: 4px solid #909399;
      }
      
      .card-content {
        display: flex;
        align-items: center;
        height: 100%;
        
        .card-icon {
          margin-right: 16px;
          color: #409eff;
        }
        
        .card-info {
          flex: 1;
          
          .card-title {
            font-size: 14px;
            color: #909399;
            margin-bottom: 8px;
          }
          
          .card-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .card-trend {
            font-size: 12px;
            display: flex;
            align-items: center;
            
            &.up {
              color: #67c23a;
            }
            
            &.down {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }
  
  .charts-section {
    margin-bottom: 20px;
    
    .chart-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .chart-container {
        height: 300px;
      }
    }
    
    .agent-ranking {
      .ranking-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .ranking-number {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 600;
          margin-right: 12px;
          
          &.rank-1 {
            background: #ffd700;
            color: white;
          }
          
          &.rank-2 {
            background: #c0c0c0;
            color: white;
          }
          
          &.rank-3 {
            background: #cd7f32;
            color: white;
          }
        }
        
        .agent-info {
          flex: 1;
          margin-left: 12px;
          
          .agent-name {
            font-weight: 500;
            margin-bottom: 2px;
          }
          
          .agent-cases {
            font-size: 12px;
            color: #909399;
          }
        }
        
        .agent-score {
          font-weight: 600;
          color: #409eff;
        }
      }
    }
  }
  
  .bottom-section {
    .quick-actions-card {
      .quick-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        
        .action-button {
          width: 100%;
          height: 48px;
        }
      }
    }
    
    .todo-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .todo-list {
        .todo-item {
          display: flex;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
          
          &.completed {
            opacity: 0.6;
            
            .todo-title {
              text-decoration: line-through;
            }
          }
          
          .todo-content {
            flex: 1;
            margin-left: 12px;
            
            .todo-title {
              font-weight: 500;
              margin-bottom: 2px;
            }
            
            .todo-time {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
      
      .todo-footer {
        text-align: center;
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;
      }
    }
    
    .activity-card {
      .activity-list {
        .activity-item {
          display: flex;
          align-items: flex-start;
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
          
          .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            
            &.case {
              background: #e1f3ff;
              color: #409eff;
            }
            
            &.payment {
              background: #f0f9e8;
              color: #67c23a;
            }
            
            &.deadline {
              background: #fef0e8;
              color: #e6a23c;
            }
            
            &.approval {
              background: #f0f9e8;
              color: #67c23a;
            }
          }
          
          .activity-content {
            flex: 1;
            
            .activity-title {
              font-weight: 500;
              margin-bottom: 4px;
            }
            
            .activity-desc {
              font-size: 12px;
              color: #606266;
              margin-bottom: 4px;
            }
            
            .activity-time {
              font-size: 11px;
              color: #909399;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard {
    .welcome-content {
      flex-direction: column;
      text-align: center;
      
      .welcome-stats {
        margin-top: 20px;
        gap: 20px;
      }
    }
    
    .quick-actions {
      grid-template-columns: 1fr !important;
    }
  }
}
</style>