<template>
  <div class="knowledge-base-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>知识库</h2>
        <p>专业知识管理与分享平台</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDocDialog = true">
          <el-icon><Plus /></el-icon>
          新增文档
        </el-button>
      </div>
    </div>

    <!-- 知识库统计 -->
    <div class="knowledge-stats">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="stat in knowledgeStats" :key="stat.key">
          <el-card class="stat-card" :class="stat.type">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="24">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入文档标题或内容关键词"
            clearable
            style="width: 300px"
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button @click="handleSearch" :loading="searching">
                <el-icon><Search /></el-icon>
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="分类">
          <el-select
            v-model="searchForm.category"
            placeholder="请选择分类"
            clearable
            style="width: 150px"
          >
            <el-option label="法律法规" value="law" />
            <el-option label="操作指南" value="guide" />
            <el-option label="案例分析" value="case" />
            <el-option label="模板文档" value="template" />
            <el-option label="培训资料" value="training" />
          </el-select>
        </el-form-item>
        <el-form-item label="标签">
          <el-select
            v-model="searchForm.tags"
            placeholder="请选择标签"
            multiple
            clearable
            style="width: 200px"
          >
            <el-option label="专利" value="patent" />
            <el-option label="商标" value="trademark" />
            <el-option label="版权" value="copyright" />
            <el-option label="诉讼" value="litigation" />
            <el-option label="合同" value="contract" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 内容区域 -->
    <el-row :gutter="20">
      <!-- 左侧分类树 -->
      <el-col :span="6">
        <el-card class="category-card">
          <template #header>
            <span>文档分类</span>
          </template>
          <el-tree
            :data="categoryTree"
            :props="treeProps"
            node-key="id"
            :default-expand-all="true"
            :highlight-current="true"
            @node-click="handleCategoryClick"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <el-icon><component :is="data.icon" /></el-icon>
                <span class="node-label">{{ node.label }}</span>
                <span class="node-count">({{ data.count }})</span>
              </div>
            </template>
          </el-tree>
        </el-card>
      </el-col>

      <!-- 右侧文档列表 -->
      <el-col :span="18">
        <el-card class="document-card">
          <template #header>
            <div class="card-header">
              <span>文档列表 ({{ total }})</span>
              <div class="header-actions">
                <el-radio-group v-model="viewMode" size="small">
                  <el-radio-button label="list">
                    <el-icon><List /></el-icon>
                  </el-radio-button>
                  <el-radio-button label="grid">
                    <el-icon><Grid /></el-icon>
                  </el-radio-button>
                </el-radio-group>
                <el-button size="small" @click="exportData">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>
            </div>
          </template>

          <!-- 列表视图 -->
          <div v-if="viewMode === 'list'" class="list-view">
            <el-table
              v-loading="loading"
              :data="documentList"
              stripe
              @row-click="viewDocument"
              style="cursor: pointer"
            >
              <el-table-column prop="title" label="文档标题" min-width="200">
                <template #default="{ row }">
                  <div class="doc-title">
                    <el-icon class="doc-icon">
                      <component :is="getDocIcon(row.type)" />
                    </el-icon>
                    <el-link type="primary" @click.stop="viewDocument(row)">
                      {{ row.title }}
                    </el-link>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="category" label="分类" width="100">
                <template #default="{ row }">
                  <el-tag :type="getCategoryColor(row.category)" size="small">
                    {{ getCategoryText(row.category) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="tags" label="标签" width="200">
                <template #default="{ row }">
                  <div class="tag-list">
                    <el-tag
                      v-for="tag in row.tags"
                      :key="tag"
                      size="small"
                      type="info"
                      style="margin-right: 4px"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="author" label="作者" width="100" />
              <el-table-column prop="views" label="浏览" width="80" align="center" />
              <el-table-column prop="downloads" label="下载" width="80" align="center" />
              <el-table-column prop="updateTime" label="更新时间" width="120">
                <template #default="{ row }">
                  {{ formatDate(row.updateTime) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" type="text" @click.stop="viewDocument(row)">
                    查看
                  </el-button>
                  <el-button size="small" type="text" @click.stop="downloadDocument(row)">
                    下载
                  </el-button>
                  <el-button size="small" type="text" @click.stop="editDocument(row)">
                    编辑
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 网格视图 -->
          <div v-else class="grid-view">
            <el-row :gutter="20">
              <el-col
                :xs="24" :sm="12" :md="8" :lg="6"
                v-for="doc in documentList"
                :key="doc.id"
              >
                <div class="doc-card" @click="viewDocument(doc)">
                  <div class="doc-header">
                    <div class="doc-icon-large">
                      <el-icon :size="32">
                        <component :is="getDocIcon(doc.type)" />
                      </el-icon>
                    </div>
                    <div class="doc-actions">
                      <el-dropdown @command="(cmd) => handleDocAction(cmd, doc)">
                        <el-button size="small" text>
                          <el-icon><MoreFilled /></el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item command="view">查看</el-dropdown-item>
                            <el-dropdown-item command="download">下载</el-dropdown-item>
                            <el-dropdown-item command="edit">编辑</el-dropdown-item>
                            <el-dropdown-item command="share">分享</el-dropdown-item>
                            <el-dropdown-item divided command="delete">删除</el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                  <div class="doc-content">
                    <h4 class="doc-title">{{ doc.title }}</h4>
                    <p class="doc-summary">{{ doc.summary }}</p>
                    <div class="doc-meta">
                      <span class="meta-item">
                        <el-icon><User /></el-icon>
                        {{ doc.author }}
                      </span>
                      <span class="meta-item">
                        <el-icon><View /></el-icon>
                        {{ doc.views }}
                      </span>
                    </div>
                    <div class="doc-tags">
                      <el-tag
                        v-for="tag in doc.tags.slice(0, 2)"
                        :key="tag"
                        size="small"
                        type="info"
                      >
                        {{ tag }}
                      </el-tag>
                      <span v-if="doc.tags.length > 2" class="more-tags">
                        +{{ doc.tags.length - 2 }}
                      </span>
                    </div>
                  </div>
                  <div class="doc-footer">
                    <span class="update-time">{{ formatDate(doc.updateTime) }}</span>
                    <el-tag :type="getCategoryColor(doc.category)" size="small">
                      {{ getCategoryText(doc.category) }}
                    </el-tag>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.size"
              :total="total"
              :page-sizes="[12, 24, 48, 96]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handlePageChange"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 新增文档对话框 -->
    <el-dialog
      v-model="showAddDocDialog"
      title="新增文档"
      width="800px"
      @close="resetDocForm"
    >
      <el-form
        ref="docFormRef"
        :model="docForm"
        :rules="docRules"
        label-width="100px"
      >
        <el-form-item label="文档标题" prop="title">
          <el-input v-model="docForm.title" placeholder="请输入文档标题" />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-select v-model="docForm.category" placeholder="请选择分类" style="width: 100%">
                <el-option label="法律法规" value="law" />
                <el-option label="操作指南" value="guide" />
                <el-option label="案例分析" value="case" />
                <el-option label="模板文档" value="template" />
                <el-option label="培训资料" value="training" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文档类型" prop="type">
              <el-select v-model="docForm.type" placeholder="请选择文档类型" style="width: 100%">
                <el-option label="Word文档" value="word" />
                <el-option label="PDF文档" value="pdf" />
                <el-option label="Excel表格" value="excel" />
                <el-option label="PowerPoint" value="ppt" />
                <el-option label="文本文档" value="text" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="标签" prop="tags">
          <el-select
            v-model="docForm.tags"
            multiple
            filterable
            allow-create
            placeholder="请选择或输入标签"
            style="width: 100%"
          >
            <el-option label="专利" value="专利" />
            <el-option label="商标" value="商标" />
            <el-option label="版权" value="版权" />
            <el-option label="诉讼" value="诉讼" />
            <el-option label="合同" value="合同" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="文档摘要">
          <el-input
            v-model="docForm.summary"
            type="textarea"
            :rows="3"
            placeholder="请输入文档摘要"
          />
        </el-form-item>
        
        <el-form-item label="文档内容" prop="content">
          <el-input
            v-model="docForm.content"
            type="textarea"
            :rows="8"
            placeholder="请输入文档内容"
          />
        </el-form-item>
        
        <el-form-item label="文件上传">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            multiple
            :auto-upload="false"
            :on-change="handleFileChange"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 doc/docx/pdf/xls/xlsx/ppt/pptx 格式文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDocDialog = false">取消</el-button>
        <el-button type="primary" @click="saveDocument" :loading="saving">
          保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 文档查看对话框 -->
    <el-dialog
      v-model="showViewDialog"
      :title="selectedDoc?.title"
      width="90%"
      top="5vh"
    >
      <div v-if="selectedDoc" class="document-viewer">
        <div class="doc-meta-info">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="分类">
              <el-tag :type="getCategoryColor(selectedDoc.category)">
                {{ getCategoryText(selectedDoc.category) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="作者">{{ selectedDoc.author }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ formatDate(selectedDoc.updateTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="浏览次数">{{ selectedDoc.views }}</el-descriptions-item>
            <el-descriptions-item label="下载次数">{{ selectedDoc.downloads }}</el-descriptions-item>
            <el-descriptions-item label="标签">
              <div>
                <el-tag
                  v-for="tag in selectedDoc.tags"
                  :key="tag"
                  size="small"
                  style="margin-right: 4px"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <el-divider content-position="left">文档摘要</el-divider>
        <p class="doc-summary-text">{{ selectedDoc.summary }}</p>
        
        <el-divider content-position="left">文档内容</el-divider>
        <div class="doc-content-text" v-html="selectedDoc.content"></div>
      </div>
      
      <template #footer>
        <el-button @click="showViewDialog = false">关闭</el-button>
        <el-button type="primary" @click="downloadDocument(selectedDoc)">
          下载文档
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Download, List, Grid, MoreFilled, User, View, UploadFilled,
  Document, Folder, Files, Reading, Notebook
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const searching = ref(false)
const saving = ref(false)
const showAddDocDialog = ref(false)
const showViewDialog = ref(false)
const docFormRef = ref()
const selectedDoc = ref(null)
const viewMode = ref('list')
const total = ref(0)

// 知识库统计数据
const knowledgeStats = reactive([
  {
    key: 'totalDocs',
    label: '文档总数',
    value: '1,234',
    icon: Document,
    type: 'primary'
  },
  {
    key: 'categories',
    label: '分类数量',
    value: '15',
    icon: Folder,
    type: 'success'
  },
  {
    key: 'totalViews',
    label: '总浏览量',
    value: '45,678',
    icon: View,
    type: 'warning'
  },
  {
    key: 'totalDownloads',
    label: '总下载量',
    value: '12,345',
    icon: Download,
    type: 'info'
  }
])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: '',
  tags: []
})

// 分页
const pagination = reactive({
  page: 1,
  size: 12
})

// 文档表单
const docForm = reactive({
  title: '',
  category: '',
  type: '',
  tags: [],
  summary: '',
  content: '',
  files: []
})

// 表单验证规则
const docRules = {
  title: [
    { required: true, message: '请输入文档标题', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择文档类型', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入文档内容', trigger: 'blur' }
  ]
}

// 分类树数据
const categoryTree = ref([
  {
    id: 'all',
    label: '全部文档',
    icon: Files,
    count: 1234,
    children: [
      { id: 'law', label: '法律法规', icon: Document, count: 234 },
      { id: 'guide', label: '操作指南', icon: Reading, count: 345 },
      { id: 'case', label: '案例分析', icon: Notebook, count: 123 },
      { id: 'template', label: '模板文档', icon: Document, count: 456 },
      { id: 'training', label: '培训资料', icon: Reading, count: 76 }
    ]
  }
])

const treeProps = {
  children: 'children',
  label: 'label'
}

// 文档列表数据
const documentList = ref([
  {
    id: 1,
    title: '专利申请流程指南',
    category: 'guide',
    type: 'pdf',
    tags: ['专利', '申请流程'],
    author: '张代理',
    views: 1234,
    downloads: 567,
    updateTime: new Date('2024-01-15'),
    summary: '详细介绍专利申请的完整流程，包括申请前准备、申请文件撰写、审查程序等各个环节。',
    content: '<h3>专利申请流程概述</h3><p>专利申请是一个复杂的法律程序...</p>'
  },
  {
    id: 2,
    title: '商标注册实务手册',
    category: 'guide',
    type: 'word',
    tags: ['商标', '注册'],
    author: '李代理',
    views: 987,
    downloads: 432,
    updateTime: new Date('2024-01-20'),
    summary: '商标注册的实务操作指南，涵盖商标检索、申请文件准备、审查流程等内容。',
    content: '<h3>商标注册基础知识</h3><p>商标是区别商品或服务来源的标志...</p>'
  },
  {
    id: 3,
    title: '知识产权侵权案例分析',
    category: 'case',
    type: 'pdf',
    tags: ['侵权', '案例分析'],
    author: '王代理',
    views: 756,
    downloads: 234,
    updateTime: new Date('2024-01-25'),
    summary: '通过典型的知识产权侵权案例，分析侵权认定标准和法律适用。',
    content: '<h3>案例一：专利侵权纠纷</h3><p>某公司生产的产品被指控侵犯他人专利权...</p>'
  }
])

// 方法
const handleSearch = () => {
  pagination.page = 1
  loadDocuments()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    category: '',
    tags: []
  })
  handleSearch()
}

const loadDocuments = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    total.value = documentList.value.length
  } catch (error) {
    ElMessage.error('加载文档列表失败')
  } finally {
    loading.value = false
  }
}

const handleCategoryClick = (data) => {
  if (data.id === 'all') {
    searchForm.category = ''
  } else {
    searchForm.category = data.id
  }
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadDocuments()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadDocuments()
}

const viewDocument = (doc) => {
  selectedDoc.value = doc
  showViewDialog.value = true
  // 增加浏览次数
  doc.views++
}

const downloadDocument = (doc) => {
  ElMessage.success(`开始下载：${doc.title}`)
  // 增加下载次数
  doc.downloads++
}

const editDocument = (doc) => {
  ElMessage.info('编辑文档功能开发中...')
}

const handleDocAction = (command, doc) => {
  switch (command) {
    case 'view':
      viewDocument(doc)
      break
    case 'download':
      downloadDocument(doc)
      break
    case 'edit':
      editDocument(doc)
      break
    case 'share':
      ElMessage.info('分享功能开发中...')
      break
    case 'delete':
      deleteDocument(doc)
      break
  }
}

const deleteDocument = async (doc) => {
  try {
    await ElMessageBox.confirm(
      `确认删除文档"${doc.title}"？`,
      '确认删除',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = documentList.value.findIndex(item => item.id === doc.id)
    if (index > -1) {
      documentList.value.splice(index, 1)
      total.value--
      ElMessage.success('删除成功')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const handleFileChange = (file, fileList) => {
  docForm.files = fileList
}

const saveDocument = async () => {
  if (!docFormRef.value) return
  
  try {
    await docFormRef.value.validate()
    saving.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('文档创建成功')
    showAddDocDialog.value = false
    loadDocuments()
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const resetDocForm = () => {
  Object.assign(docForm, {
    title: '',
    category: '',
    type: '',
    tags: [],
    summary: '',
    content: '',
    files: []
  })
  if (docFormRef.value) {
    docFormRef.value.clearValidate()
  }
}

// 辅助方法
const getCategoryText = (category) => {
  const categoryMap = {
    law: '法律法规',
    guide: '操作指南',
    case: '案例分析',
    template: '模板文档',
    training: '培训资料'
  }
  return categoryMap[category] || category
}

const getCategoryColor = (category) => {
  const colorMap = {
    law: 'danger',
    guide: 'primary',
    case: 'warning',
    template: 'success',
    training: 'info'
  }
  return colorMap[category] || 'info'
}

const getDocIcon = (type) => {
  const iconMap = {
    word: Document,
    pdf: Document,
    excel: Document,
    ppt: Document,
    text: Document
  }
  return iconMap[type] || Document
}

const formatDate = (date) => {
  return date.toLocaleDateString('zh-CN')
}

// 组件挂载
onMounted(() => {
  loadDocuments()
})
</script>

<style lang="scss" scoped>
.knowledge-base-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      h2 {
        margin: 0 0 4px 0;
        color: #303133;
      }
      
      p {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .knowledge-stats {
    margin-bottom: 20px;
    
    .stat-card {
      height: 80px;
      
      &.primary {
        border-left: 4px solid #409eff;
      }
      
      &.success {
        border-left: 4px solid #67c23a;
      }
      
      &.warning {
        border-left: 4px solid #e6a23c;
      }
      
      &.info {
        border-left: 4px solid #909399;
      }
      
      .stat-content {
        display: flex;
        align-items: center;
        height: 100%;
        
        .stat-icon {
          margin-right: 16px;
          color: #409eff;
        }
        
        .stat-info {
          .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .category-card {
    height: fit-content;
    
    .tree-node {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .node-label {
        flex: 1;
      }
      
      .node-count {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .document-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
    
    .list-view {
      .doc-title {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .doc-icon {
          color: #409eff;
        }
      }
      
      .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }
    }
    
    .grid-view {
      .doc-card {
        border: 1px solid #ebeef5;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        cursor: pointer;
        transition: all 0.3s;
        height: 280px;
        display: flex;
        flex-direction: column;
        
        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
        }
        
        .doc-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          
          .doc-icon-large {
            color: #409eff;
          }
        }
        
        .doc-content {
          flex: 1;
          
          .doc-title {
            font-size: 16px;
            font-weight: 500;
            margin: 0 0 8px 0;
            color: #303133;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          
          .doc-summary {
            font-size: 14px;
            color: #606266;
            margin: 0 0 12px 0;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.5;
          }
          
          .doc-meta {
            display: flex;
            gap: 16px;
            margin-bottom: 12px;
            
            .meta-item {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              color: #909399;
            }
          }
          
          .doc-tags {
            display: flex;
            gap: 4px;
            align-items: center;
            
            .more-tags {
              font-size: 12px;
              color: #909399;
            }
          }
        }
        
        .doc-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 12px;
          padding-top: 12px;
          border-top: 1px solid #f0f0f0;
          
          .update-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
    
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
  
  .document-viewer {
    .doc-meta-info {
      margin-bottom: 20px;
    }
    
    .doc-summary-text {
      line-height: 1.8;
      color: #606266;
      margin: 16px 0;
    }
    
    .doc-content-text {
      line-height: 1.8;
      color: #303133;
      
      :deep(h3) {
        color: #409eff;
        margin: 20px 0 12px 0;
      }
      
      :deep(p) {
        margin: 12px 0;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .knowledge-base-page {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
    
    .search-card {
      :deep(.el-form--inline) {
        .el-form-item {
          display: block;
          margin-right: 0;
          margin-bottom: 16px;
        }
      }
    }
    
    .grid-view {
      .doc-card {
        height: auto;
        min-height: 200px;
      }
    }
  }
}
</style>