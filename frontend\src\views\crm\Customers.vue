<template>
  <div class="customers-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>客户管理</h2>
        <p>管理客户档案和联系信息</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          新增客户
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="客户名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入客户名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="客户类型">
          <el-select
            v-model="searchForm.type"
            placeholder="请选择客户类型"
            clearable
            style="width: 150px"
          >
            <el-option label="企业客户" value="enterprise" />
            <el-option label="个人客户" value="individual" />
            <el-option label="代理机构" value="agency" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择客户状态"
            clearable
            style="width: 150px"
          >
            <el-option label="活跃" value="active" />
            <el-option label="潜在" value="potential" />
            <el-option label="暂停" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 客户列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>客户列表 ({{ total }})</span>
          <div class="header-actions">
            <el-button size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
            <el-button size="small" @click="importData">
              <el-icon><Upload /></el-icon>
              导入
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="customerList"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="客户名称" min-width="150">
          <template #default="{ row }">
            <div class="customer-info">
              <el-avatar :size="32" :src="row.avatar">
                {{ row.name.charAt(0) }}
              </el-avatar>
              <div class="info-text">
                <div class="name">{{ row.name }}</div>
                <div class="code">{{ row.code }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="客户类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)" size="small">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="contact" label="联系人" width="120" />
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column prop="email" label="邮箱" min-width="180" />
        <el-table-column prop="caseCount" label="案件数量" width="100" align="center">
          <template #default="{ row }">
            <el-link type="primary" @click="viewCases(row)">
              {{ row.caseCount }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" label="总金额" width="120" align="right">
          <template #default="{ row }">
            <span class="amount">¥{{ formatAmount(row.totalAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="text" @click="viewCustomer(row)">
              查看
            </el-button>
            <el-button size="small" type="text" @click="editCustomer(row)">
              编辑
            </el-button>
            <el-button size="small" type="text" @click="viewCases(row)">
              案件
            </el-button>
            <el-dropdown @command="(cmd) => handleMoreAction(cmd, row)">
              <el-button size="small" type="text">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="contact">联系记录</el-dropdown-item>
                  <el-dropdown-item command="contract">合同管理</el-dropdown-item>
                  <el-dropdown-item command="finance">财务记录</el-dropdown-item>
                  <el-dropdown-item divided command="delete" class="danger">
                    删除客户
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑客户对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingCustomer ? '编辑客户' : '新增客户'"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="customerFormRef"
        :model="customerForm"
        :rules="customerRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户名称" prop="name">
              <el-input v-model="customerForm.name" placeholder="请输入客户名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户编码" prop="code">
              <el-input v-model="customerForm.code" placeholder="自动生成" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户类型" prop="type">
              <el-select v-model="customerForm.type" placeholder="请选择客户类型" style="width: 100%">
                <el-option label="企业客户" value="enterprise" />
                <el-option label="个人客户" value="individual" />
                <el-option label="代理机构" value="agency" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户状态" prop="status">
              <el-select v-model="customerForm.status" placeholder="请选择客户状态" style="width: 100%">
                <el-option label="活跃" value="active" />
                <el-option label="潜在" value="potential" />
                <el-option label="暂停" value="inactive" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contact">
              <el-input v-model="customerForm.contact" placeholder="请输入联系人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="customerForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="customerForm.email" placeholder="请输入邮箱地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="传真">
              <el-input v-model="customerForm.fax" placeholder="请输入传真号码" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="地址">
          <el-input v-model="customerForm.address" placeholder="请输入详细地址" />
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="customerForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCustomer" :loading="saving">
          {{ editingCustomer ? '更新' : '保存' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Download, Upload, ArrowDown
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showAddDialog = ref(false)
const editingCustomer = ref(null)
const customerFormRef = ref()
const selectedCustomers = ref([])
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  name: '',
  type: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20
})

// 客户表单
const customerForm = reactive({
  name: '',
  code: '',
  type: '',
  status: 'active',
  contact: '',
  phone: '',
  email: '',
  fax: '',
  address: '',
  remark: ''
})

// 表单验证规则
const customerRules = {
  name: [
    { required: true, message: '请输入客户名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择客户类型', trigger: 'change' }
  ],
  contact: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 客户列表数据
const customerList = ref([
  {
    id: 1,
    name: '北京科技有限公司',
    code: 'CUS001',
    type: 'enterprise',
    contact: '张经理',
    phone: '13800138001',
    email: '<EMAIL>',
    caseCount: 15,
    totalAmount: 250000,
    status: 'active',
    createTime: new Date('2023-01-15'),
    avatar: ''
  },
  {
    id: 2,
    name: '上海创新科技',
    code: 'CUS002',
    type: 'enterprise',
    contact: '李总',
    phone: '13900139002',
    email: '<EMAIL>',
    caseCount: 8,
    totalAmount: 120000,
    status: 'active',
    createTime: new Date('2023-02-20'),
    avatar: ''
  },
  {
    id: 3,
    name: '王发明',
    code: 'CUS003',
    type: 'individual',
    contact: '王发明',
    phone: '13700137003',
    email: '<EMAIL>',
    caseCount: 3,
    totalAmount: 45000,
    status: 'potential',
    createTime: new Date('2023-03-10'),
    avatar: ''
  },
  {
    id: 4,
    name: '深圳知识产权代理',
    code: 'CUS004',
    type: 'agency',
    contact: '陈代理',
    phone: '13600136004',
    email: '<EMAIL>',
    caseCount: 25,
    totalAmount: 380000,
    status: 'active',
    createTime: new Date('2023-01-05'),
    avatar: ''
  }
])

// 方法
const handleSearch = () => {
  pagination.page = 1
  loadCustomers()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    type: '',
    status: ''
  })
  handleSearch()
}

const loadCustomers = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    total.value = customerList.value.length
  } catch (error) {
    ElMessage.error('加载客户列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedCustomers.value = selection
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadCustomers()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadCustomers()
}

const viewCustomer = (customer) => {
  router.push(`/crm/customers/${customer.id}`)
}

const editCustomer = (customer) => {
  editingCustomer.value = customer
  Object.assign(customerForm, customer)
  showAddDialog.value = true
}

const viewCases = (customer) => {
  router.push(`/cases/list?customerId=${customer.id}`)
}

const handleMoreAction = async (command, customer) => {
  switch (command) {
    case 'contact':
      router.push(`/crm/customers/${customer.id}/contacts`)
      break
    case 'contract':
      router.push(`/crm/customers/${customer.id}/contracts`)
      break
    case 'finance':
      router.push(`/finance/records?customerId=${customer.id}`)
      break
    case 'delete':
      try {
        await ElMessageBox.confirm(
          `确定要删除客户 "${customer.name}" 吗？此操作不可恢复。`,
          '删除确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        // 执行删除操作
        ElMessage.success('客户删除成功')
        loadCustomers()
      } catch (error) {
        // 用户取消
      }
      break
  }
}

const saveCustomer = async () => {
  if (!customerFormRef.value) return
  
  try {
    await customerFormRef.value.validate()
    saving.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingCustomer.value) {
      ElMessage.success('客户信息更新成功')
    } else {
      // 生成客户编码
      customerForm.code = `CUS${String(Date.now()).slice(-6)}`
      ElMessage.success('客户创建成功')
    }
    
    showAddDialog.value = false
    loadCustomers()
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  editingCustomer.value = null
  Object.assign(customerForm, {
    name: '',
    code: '',
    type: '',
    status: 'active',
    contact: '',
    phone: '',
    email: '',
    fax: '',
    address: '',
    remark: ''
  })
  if (customerFormRef.value) {
    customerFormRef.value.clearValidate()
  }
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const importData = () => {
  ElMessage.info('导入功能开发中...')
}

// 辅助方法
const getTypeText = (type) => {
  const typeMap = {
    enterprise: '企业',
    individual: '个人',
    agency: '代理'
  }
  return typeMap[type] || type
}

const getTypeColor = (type) => {
  const colorMap = {
    enterprise: 'primary',
    individual: 'success',
    agency: 'warning'
  }
  return colorMap[type] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    active: '活跃',
    potential: '潜在',
    inactive: '暂停'
  }
  return statusMap[status] || status
}

const getStatusColor = (status) => {
  const colorMap = {
    active: 'success',
    potential: 'warning',
    inactive: 'info'
  }
  return colorMap[status] || 'info'
}

const formatAmount = (amount) => {
  return amount.toLocaleString()
}

const formatDate = (date) => {
  return date.toLocaleDateString('zh-CN')
}

// 组件挂载
onMounted(() => {
  loadCustomers()
})
</script>

<style lang="scss" scoped>
.customers-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      h2 {
        margin: 0 0 4px 0;
        color: #303133;
      }
      
      p {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .customer-info {
      display: flex;
      align-items: center;
      
      .info-text {
        margin-left: 12px;
        
        .name {
          font-weight: 500;
          margin-bottom: 2px;
        }
        
        .code {
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    .amount {
      font-weight: 600;
      color: #67c23a;
    }
    
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}

:deep(.danger) {
  color: #f56c6c;
}

// 响应式设计
@media (max-width: 768px) {
  .customers-page {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
    
    .search-card {
      :deep(.el-form--inline) {
        .el-form-item {
          display: block;
          margin-right: 0;
          margin-bottom: 16px;
        }
      }
    }
  }
}
</style>