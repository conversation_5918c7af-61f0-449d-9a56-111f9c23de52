<template>
  <div class="finance-overview-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>财务管理</h2>
        <p>案件费用管理与财务统计分析</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddFeeDialog = true">
          <el-icon><Plus /></el-icon>
          新增费用
        </el-button>
      </div>
    </div>

    <!-- 财务概览 -->
    <div class="finance-stats">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="stat in financeStats" :key="stat.key">
          <el-card class="stat-card" :class="stat.type">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon :size="28">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-change" :class="stat.changeType">
                  <el-icon><component :is="stat.changeIcon" /></el-icon>
                  {{ stat.change }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>月度收支趋势</span>
          </template>
          <div ref="revenueChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>费用类型分布</span>
          </template>
          <div ref="feeTypeChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选和搜索 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="费用类型">
          <el-select
            v-model="filterForm.feeType"
            placeholder="请选择费用类型"
            clearable
            style="width: 150px"
          >
            <el-option label="申请费" value="application" />
            <el-option label="审查费" value="examination" />
            <el-option label="授权费" value="grant" />
            <el-option label="年费" value="annual" />
            <el-option label="代理费" value="agency" />
          </el-select>
        </el-form-item>
        <el-form-item label="案件编号">
          <el-input
            v-model="filterForm.caseNumber"
            placeholder="请输入案件编号"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="filterForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="待支付" value="pending" />
            <el-option label="已支付" value="paid" />
            <el-option label="已逾期" value="overdue" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">
            <el-icon><Search /></el-icon>
            筛选
          </el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 费用列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>费用记录 ({{ total }})</span>
          <div class="header-actions">
            <el-button size="small" @click="batchPayment">
              <el-icon><CreditCard /></el-icon>
              批量支付
            </el-button>
            <el-button size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="feeList"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="caseNumber" label="案件编号" width="140">
          <template #default="{ row }">
            <el-link type="primary" @click="viewCase(row.caseNumber)">
              {{ row.caseNumber }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="caseTitle" label="案件名称" min-width="200" />
        <el-table-column prop="feeType" label="费用类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getFeeTypeColor(row.feeType)" size="small">
              {{ getFeeTypeText(row.feeType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="金额" width="120" align="right">
          <template #default="{ row }">
            <span class="amount" :class="getAmountClass(row.type)">
              {{ row.type === 'income' ? '+' : '-' }}¥{{ formatAmount(row.amount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="dueDate" label="到期日期" width="120">
          <template #default="{ row }">
            <span :class="getDueDateClass(row.dueDate, row.status)">
              {{ formatDate(row.dueDate) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentDate" label="支付日期" width="120">
          <template #default="{ row }">
            {{ row.paymentDate ? formatDate(row.paymentDate) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="client" label="客户" width="150" />
        <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'pending'"
              size="small"
              type="primary"
              @click="payFee(row)"
            >
              支付
            </el-button>
            <el-button size="small" type="text" @click="editFee(row)">
              编辑
            </el-button>
            <el-button size="small" type="text" @click="viewFeeDetail(row)">
              详情
            </el-button>
            <el-dropdown @command="(cmd) => handleMoreAction(cmd, row)">
              <el-button size="small" type="text">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="invoice">开具发票</el-dropdown-item>
                  <el-dropdown-item command="receipt">查看收据</el-dropdown-item>
                  <el-dropdown-item divided command="delete" v-if="row.status === 'pending'">
                    删除记录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 新增费用对话框 -->
    <el-dialog
      v-model="showAddFeeDialog"
      title="新增费用"
      width="600px"
      @close="resetFeeForm"
    >
      <el-form
        ref="feeFormRef"
        :model="feeForm"
        :rules="feeRules"
        label-width="100px"
      >
        <el-form-item label="案件" prop="caseId">
          <el-select
            v-model="feeForm.caseId"
            placeholder="请选择案件"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="caseItem in caseOptions"
              :key="caseItem.id"
              :label="`${caseItem.caseNumber} - ${caseItem.title}`"
              :value="caseItem.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="费用类型" prop="feeType">
          <el-select v-model="feeForm.feeType" placeholder="请选择费用类型" style="width: 100%">
            <el-option label="申请费" value="application" />
            <el-option label="审查费" value="examination" />
            <el-option label="授权费" value="grant" />
            <el-option label="年费" value="annual" />
            <el-option label="代理费" value="agency" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="收支类型" prop="type">
          <el-radio-group v-model="feeForm.type">
            <el-radio label="income">收入</el-radio>
            <el-radio label="expense">支出</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="金额" prop="amount">
          <el-input-number
            v-model="feeForm.amount"
            :min="0"
            :precision="2"
            placeholder="请输入金额"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="到期日期" prop="dueDate">
          <el-date-picker
            v-model="feeForm.dueDate"
            type="date"
            placeholder="请选择到期日期"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="feeForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddFeeDialog = false">取消</el-button>
        <el-button type="primary" @click="saveFee" :loading="saving">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Download, CreditCard, ArrowDown,
  Money, TrendCharts, Wallet, Warning, ArrowUp, ArrowDown as ArrowDownIcon
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showAddFeeDialog = ref(false)
const feeFormRef = ref()
const selectedFees = ref([])
const total = ref(0)
const revenueChartRef = ref()
const feeTypeChartRef = ref()

// 财务统计数据
const financeStats = reactive([
  {
    key: 'totalRevenue',
    label: '总收入',
    value: '¥1,234,567',
    icon: Money,
    type: 'success',
    change: '12.5%',
    changeType: 'increase',
    changeIcon: ArrowUp
  },
  {
    key: 'totalExpense',
    label: '总支出',
    value: '¥456,789',
    icon: Wallet,
    type: 'warning',
    change: '8.3%',
    changeType: 'increase',
    changeIcon: ArrowUp
  },
  {
    key: 'netProfit',
    label: '净利润',
    value: '¥777,778',
    icon: TrendCharts,
    type: 'primary',
    change: '15.2%',
    changeType: 'increase',
    changeIcon: ArrowUp
  },
  {
    key: 'pendingPayment',
    label: '待支付',
    value: '¥89,123',
    icon: Warning,
    type: 'danger',
    change: '5.7%',
    changeType: 'decrease',
    changeIcon: ArrowDownIcon
  }
])

// 筛选表单
const filterForm = reactive({
  dateRange: [],
  feeType: '',
  caseNumber: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20
})

// 费用表单
const feeForm = reactive({
  caseId: '',
  feeType: '',
  type: 'expense',
  amount: 0,
  dueDate: '',
  remark: ''
})

// 表单验证规则
const feeRules = {
  caseId: [
    { required: true, message: '请选择案件', trigger: 'change' }
  ],
  feeType: [
    { required: true, message: '请选择费用类型', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择收支类型', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
  ],
  dueDate: [
    { required: true, message: '请选择到期日期', trigger: 'change' }
  ]
}

// 案件选项
const caseOptions = ref([
  { id: 1, caseNumber: 'ZL202301001', title: '一种新型智能手机充电器' },
  { id: 2, caseNumber: 'ZL202301002', title: '智能家居控制系统' },
  { id: 3, caseNumber: 'TM202301003', title: '创新科技商标' }
])

// 费用列表数据
const feeList = ref([
  {
    id: 1,
    caseNumber: 'ZL202301001',
    caseTitle: '一种新型智能手机充电器',
    feeType: 'application',
    type: 'expense',
    amount: 950,
    dueDate: new Date('2024-03-15'),
    status: 'paid',
    paymentDate: new Date('2024-02-20'),
    client: '北京科技有限公司',
    remark: '发明专利申请费'
  },
  {
    id: 2,
    caseNumber: 'ZL202301001',
    caseTitle: '一种新型智能手机充电器',
    feeType: 'agency',
    type: 'income',
    amount: 15000,
    dueDate: new Date('2024-02-10'),
    status: 'paid',
    paymentDate: new Date('2024-02-08'),
    client: '北京科技有限公司',
    remark: '代理服务费'
  },
  {
    id: 3,
    caseNumber: 'ZL202301002',
    caseTitle: '智能家居控制系统',
    feeType: 'examination',
    type: 'expense',
    amount: 2500,
    dueDate: new Date('2024-04-20'),
    status: 'pending',
    paymentDate: null,
    client: '上海创新科技',
    remark: '实质审查费'
  },
  {
    id: 4,
    caseNumber: 'TM202301003',
    caseTitle: '创新科技商标',
    feeType: 'application',
    type: 'expense',
    amount: 300,
    dueDate: new Date('2024-01-15'),
    status: 'overdue',
    paymentDate: null,
    client: '王发明',
    remark: '商标注册费'
  }
])

// 方法
const handleFilter = () => {
  pagination.page = 1
  loadFees()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    dateRange: [],
    feeType: '',
    caseNumber: '',
    status: ''
  })
  handleFilter()
}

const loadFees = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    total.value = feeList.value.length
  } catch (error) {
    ElMessage.error('加载费用列表失败')
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedFees.value = selection
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadFees()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadFees()
}

const viewCase = (caseNumber) => {
  router.push(`/cases?search=${caseNumber}`)
}

const payFee = async (fee) => {
  try {
    await ElMessageBox.confirm(
      `确认支付费用 ¥${formatAmount(fee.amount)}？`,
      '确认支付',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟支付API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    fee.status = 'paid'
    fee.paymentDate = new Date()
    ElMessage.success('支付成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('支付失败')
    }
  }
}

const editFee = (fee) => {
  ElMessage.info('编辑费用功能开发中...')
}

const viewFeeDetail = (fee) => {
  ElMessage.info('费用详情功能开发中...')
}

const handleMoreAction = (command, fee) => {
  switch (command) {
    case 'invoice':
      ElMessage.info('开具发票功能开发中...')
      break
    case 'receipt':
      ElMessage.info('查看收据功能开发中...')
      break
    case 'delete':
      deleteFee(fee)
      break
  }
}

const deleteFee = async (fee) => {
  try {
    await ElMessageBox.confirm(
      '确认删除此费用记录？',
      '确认删除',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = feeList.value.findIndex(item => item.id === fee.id)
    if (index > -1) {
      feeList.value.splice(index, 1)
      total.value--
      ElMessage.success('删除成功')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const batchPayment = () => {
  if (selectedFees.value.length === 0) {
    ElMessage.warning('请先选择要支付的费用')
    return
  }
  ElMessage.info('批量支付功能开发中...')
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const saveFee = async () => {
  if (!feeFormRef.value) return
  
  try {
    await feeFormRef.value.validate()
    saving.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('费用记录创建成功')
    showAddFeeDialog.value = false
    loadFees()
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const resetFeeForm = () => {
  Object.assign(feeForm, {
    caseId: '',
    feeType: '',
    type: 'expense',
    amount: 0,
    dueDate: '',
    remark: ''
  })
  if (feeFormRef.value) {
    feeFormRef.value.clearValidate()
  }
}

// 图表初始化
const initCharts = () => {
  nextTick(() => {
    initRevenueChart()
    initFeeTypeChart()
  })
}

const initRevenueChart = () => {
  if (!revenueChartRef.value) return
  
  const chart = echarts.init(revenueChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['收入', '支出', '净利润']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}'
      }
    },
    series: [
      {
        name: '收入',
        type: 'line',
        data: [120000, 132000, 101000, 134000, 90000, 230000],
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '支出',
        type: 'line',
        data: [80000, 88000, 65000, 89000, 60000, 150000],
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '净利润',
        type: 'line',
        data: [40000, 44000, 36000, 45000, 30000, 80000],
        itemStyle: { color: '#409eff' }
      }
    ]
  }
  chart.setOption(option)
}

const initFeeTypeChart = () => {
  if (!feeTypeChartRef.value) return
  
  const chart = echarts.init(feeTypeChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '费用类型',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 150000, name: '代理费' },
          { value: 80000, name: '申请费' },
          { value: 60000, name: '审查费' },
          { value: 40000, name: '授权费' },
          { value: 30000, name: '年费' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 辅助方法
const getFeeTypeText = (type) => {
  const typeMap = {
    application: '申请费',
    examination: '审查费',
    grant: '授权费',
    annual: '年费',
    agency: '代理费'
  }
  return typeMap[type] || type
}

const getFeeTypeColor = (type) => {
  const colorMap = {
    application: 'primary',
    examination: 'warning',
    grant: 'success',
    annual: 'info',
    agency: 'danger'
  }
  return colorMap[type] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '待支付',
    paid: '已支付',
    overdue: '已逾期'
  }
  return statusMap[status] || status
}

const getStatusColor = (status) => {
  const colorMap = {
    pending: 'warning',
    paid: 'success',
    overdue: 'danger'
  }
  return colorMap[status] || 'info'
}

const getAmountClass = (type) => {
  return type === 'income' ? 'income' : 'expense'
}

const getDueDateClass = (dueDate, status) => {
  if (status === 'paid') return 'normal'
  
  const now = new Date()
  const diff = dueDate - now
  const days = Math.ceil(diff / (1000 * 60 * 60 * 24))
  
  if (days < 0) return 'overdue'
  if (days <= 7) return 'urgent'
  if (days <= 30) return 'warning'
  return 'normal'
}

const formatAmount = (amount) => {
  return amount.toLocaleString()
}

const formatDate = (date) => {
  return date.toLocaleDateString('zh-CN')
}

// 组件挂载
onMounted(() => {
  loadFees()
  initCharts()
})
</script>

<style lang="scss" scoped>
.finance-overview-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      h2 {
        margin: 0 0 4px 0;
        color: #303133;
      }
      
      p {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .finance-stats {
    margin-bottom: 20px;
    
    .stat-card {
      height: 100px;
      
      &.primary {
        border-left: 4px solid #409eff;
      }
      
      &.success {
        border-left: 4px solid #67c23a;
      }
      
      &.warning {
        border-left: 4px solid #e6a23c;
      }
      
      &.danger {
        border-left: 4px solid #f56c6c;
      }
      
      .stat-content {
        display: flex;
        align-items: center;
        height: 100%;
        
        .stat-icon {
          margin-right: 16px;
          color: #409eff;
        }
        
        .stat-info {
          flex: 1;
          
          .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }
          
          .stat-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 2px;
            
            &.increase {
              color: #67c23a;
            }
            
            &.decrease {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }
  
  .charts-section {
    margin-bottom: 20px;
    
    .chart-card {
      .chart-container {
        height: 300px;
      }
    }
  }
  
  .filter-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .amount {
      font-weight: 600;
      
      &.income {
        color: #67c23a;
      }
      
      &.expense {
        color: #f56c6c;
      }
    }
    
    .overdue {
      color: #f56c6c;
      font-weight: 600;
    }
    
    .urgent {
      color: #e6a23c;
      font-weight: 600;
    }
    
    .warning {
      color: #e6a23c;
    }
    
    .normal {
      color: #303133;
    }
    
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .finance-overview-page {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
    
    .charts-section {
      .el-col {
        margin-bottom: 20px;
      }
    }
    
    .filter-card {
      :deep(.el-form--inline) {
        .el-form-item {
          display: block;
          margin-right: 0;
          margin-bottom: 16px;
        }
      }
    }
  }
}
</style>