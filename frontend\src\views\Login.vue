<template>
  <div class="login-container">
    <div class="login-background">
      <div class="background-overlay"></div>
    </div>
    
    <div class="login-form-container">
      <div class="login-form">
        <div class="login-header">
          <div class="logo">
            <el-icon size="40" color="#409eff">
              <Document />
            </el-icon>
          </div>
          <h1 class="title">智邦知识产权数字化系统</h1>
          <p class="subtitle">专业的IP代理业务管理平台</p>
        </div>
        
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form-content"
          size="large"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              prefix-icon="User"
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock"
              show-password
              clearable
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          
          <el-form-item prop="captcha" v-if="showCaptcha">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.captcha"
                placeholder="请输入验证码"
                prefix-icon="Key"
                clearable
                style="flex: 1; margin-right: 10px;"
              />
              <div class="captcha-image" @click="refreshCaptcha">
                <img :src="captchaUrl" alt="验证码" />
              </div>
            </div>
          </el-form-item>
          
          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model="loginForm.rememberMe">
                记住我
              </el-checkbox>
              <el-link type="primary" :underline="false">
                忘记密码？
              </el-link>
            </div>
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              style="width: 100%;"
              :loading="loading"
              @click="handleLogin"
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="login-footer">
          <div class="demo-accounts">
            <p class="demo-title">演示账号</p>
            <div class="demo-buttons">
              <el-button
                size="small"
                type="info"
                plain
                @click="setDemoAccount('admin')"
              >
                管理员
              </el-button>
              <el-button
                size="small"
                type="info"
                plain
                @click="setDemoAccount('agent')"
              >
                代理人
              </el-button>
              <el-button
                size="small"
                type="info"
                plain
                @click="setDemoAccount('bd')"
              >
                商务
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { Document, User, Lock, Key } from '@element-plus/icons-vue'

const userStore = useUserStore()
const loginFormRef = ref()
const loading = ref(false)
const showCaptcha = ref(false)
const captchaUrl = ref('')

// 表单数据
const loginForm = reactive({
  username: '',
  password: '',
  captcha: '',
  rememberMe: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码长度为 4 个字符', trigger: 'blur' }
  ]
}

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    loading.value = true
    
    // 模拟登录API调用
    await simulateLogin()
    
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}

// 模拟登录
const simulateLogin = async () => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 模拟登录验证
      const validAccounts = {
        'admin': { password: '123456', role: 'admin', name: '系统管理员' },
        'agent': { password: '123456', role: 'agent', name: '专利代理人' },
        'bd': { password: '123456', role: 'bd', name: '商务专员' },
        'demo': { password: '123456', role: 'user', name: '演示用户' }
      }
      
      const account = validAccounts[loginForm.username]
      
      if (!account || account.password !== loginForm.password) {
        ElMessage.error('用户名或密码错误')
        reject(new Error('用户名或密码错误'))
        return
      }
      
      // 模拟成功响应
      const mockResponse = {
        data: {
          token: 'mock-jwt-token-' + Date.now(),
          user: {
            id: Math.floor(Math.random() * 1000),
            username: loginForm.username,
            name: account.name,
            role: account.role,
            email: `${loginForm.username}@zhibang.com`,
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            department: account.role === 'admin' ? '管理部' : account.role === 'agent' ? '代理部' : '商务部',
            phone: '138****8888'
          },
          permissions: account.role === 'admin' ? ['*'] : [
            'dashboard:view',
            'crm:view',
            'cases:view',
            account.role === 'agent' ? 'cases:edit' : 'crm:edit'
          ]
        }
      }
      
      userStore.loginAction(mockResponse.data)
      resolve(mockResponse)
    }, 1000)
  })
}

// 设置演示账号
const setDemoAccount = (type) => {
  const accounts = {
    admin: { username: 'admin', password: '123456' },
    agent: { username: 'agent', password: '123456' },
    bd: { username: 'bd', password: '123456' }
  }
  
  const account = accounts[type]
  if (account) {
    loginForm.username = account.username
    loginForm.password = account.password
    ElMessage.success(`已填入${type === 'admin' ? '管理员' : type === 'agent' ? '代理人' : '商务'}演示账号`)
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  captchaUrl.value = `/api/captcha?t=${Date.now()}`
}

// 组件挂载
onMounted(() => {
  // 如果需要验证码，则显示
  if (showCaptcha.value) {
    refreshCaptcha()
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  .background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
    opacity: 0.3;
  }
}

.login-form-container {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 400px;
  padding: 0 20px;
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  
  .logo {
    margin-bottom: 16px;
  }
  
  .title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
  }
  
  .subtitle {
    font-size: 14px;
    color: #909399;
    margin: 0;
  }
}

.login-form-content {
  .el-form-item {
    margin-bottom: 24px;
  }
  
  .captcha-container {
    display: flex;
    align-items: center;
    
    .captcha-image {
      width: 100px;
      height: 40px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      cursor: pointer;
      overflow: hidden;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  
  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
}

.login-footer {
  margin-top: 30px;
  
  .demo-accounts {
    text-align: center;
    
    .demo-title {
      font-size: 12px;
      color: #909399;
      margin-bottom: 10px;
    }
    
    .demo-buttons {
      display: flex;
      gap: 8px;
      justify-content: center;
      flex-wrap: wrap;
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-form-container {
    padding: 0 16px;
  }
  
  .login-form {
    padding: 30px 20px;
  }
  
  .login-header .title {
    font-size: 20px;
  }
}

// 动画效果
.login-form {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>