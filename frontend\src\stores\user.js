import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import router from '@/router'
import { login, logout, getUserInfo } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  const token = ref(localStorage.getItem('token') || '')
  
  // 安全地解析localStorage中的数据
  const getUserInfoFromStorage = () => {
    try {
      const stored = localStorage.getItem('userInfo')
      return stored ? JSON.parse(stored) : null
    } catch (error) {
      console.warn('Failed to parse userInfo from localStorage:', error)
      return null
    }
  }
  
  const getPermissionsFromStorage = () => {
    try {
      const stored = localStorage.getItem('permissions')
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.warn('Failed to parse permissions from localStorage:', error)
      return []
    }
  }
  
  const userInfo = ref(getUserInfoFromStorage())
  const permissions = ref(getPermissionsFromStorage())
  
  const isAuthenticated = computed(() => {
    return !!token.value && !!userInfo.value && !!userInfo.value.id
  })
  
  const hasPermission = computed(() => {
    return (permission) => {
      if (!permissions.value || !Array.isArray(permissions.value)) {
        return false
      }
      return permissions.value.includes(permission) || permissions.value.includes('*')
    }
  })
  
  // 登录
  const loginAction = async (loginData) => {
    try {
      let response
      
      // 如果传入的是已经处理好的数据（包含token、user、permissions），直接使用
      if (loginData.token && loginData.user && loginData.permissions) {
        response = { data: loginData }
      } else {
        // 否则调用API
        response = await login(loginData)
      }
      
      const { token: newToken, user, permissions: userPermissions } = response.data
      
      token.value = newToken
      userInfo.value = user
      permissions.value = userPermissions
      
      // 保存到本地存储
      localStorage.setItem('token', newToken)
      localStorage.setItem('userInfo', JSON.stringify(user))
      localStorage.setItem('permissions', JSON.stringify(userPermissions))
      
      ElMessage.success('登录成功')
      router.push('/dashboard')
      
      return response
    } catch (error) {
      ElMessage.error(error.message || '登录失败')
      throw error
    }
  }
  
  // 登出
  const logoutAction = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('登出接口调用失败:', error)
    } finally {
      // 清除本地数据
      token.value = ''
      userInfo.value = {}
      permissions.value = []
      
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      localStorage.removeItem('permissions')
      
      router.push('/login')
      ElMessage.success('已退出登录')
    }
  }
  
  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      const response = await getUserInfo()
      const { user, permissions: userPermissions } = response.data
      
      userInfo.value = user
      permissions.value = userPermissions
      
      localStorage.setItem('userInfo', JSON.stringify(user))
      localStorage.setItem('permissions', JSON.stringify(userPermissions))
      
      return response
    } catch (error) {
      ElMessage.error('获取用户信息失败')
      throw error
    }
  }
  
  // 检查登录状态
  const checkLoginStatus = () => {
    const savedToken = localStorage.getItem('token')
    const savedUserInfo = getUserInfoFromStorage()
    const savedPermissions = getPermissionsFromStorage()
    
    if (savedToken && savedUserInfo) {
      token.value = savedToken
      userInfo.value = savedUserInfo
      permissions.value = savedPermissions
    }
  }
  
  return {
    token,
    userInfo,
    permissions,
    isAuthenticated,
    hasPermission,
    loginAction,
    logoutAction,
    getUserInfoAction,
    checkLoginStatus
  }
})