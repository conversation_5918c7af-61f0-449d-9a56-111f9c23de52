import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/layout/index.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'DashboardHome',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '工作台' }
      }
    ]
  },
  {
    path: '/crm',
    name: 'CRM',
    component: () => import('@/layout/index.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'customers',
        name: 'Customers',
        component: () => import('@/views/crm/Customers.vue'),
        meta: { title: '客户管理' }
      },
      {
        path: 'leads',
        name: 'Leads',
        component: () => import('@/views/crm/Leads.vue'),
        meta: { title: '线索管理' }
      },
      {
        path: 'opportunities',
        name: 'Opportunities',
        component: () => import('@/views/crm/Opportunities.vue'),
        meta: { title: '商机管理' }
      }
    ]
  },
  {
    path: '/cases',
    name: 'Cases',
    component: () => import('@/layout/index.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'patent',
        name: 'PatentCases',
        component: () => import('@/views/cases/CaseList.vue'),
        meta: { title: '专利案件' }
      },
      {
        path: 'trademark',
        name: 'TrademarkCases',
        component: () => import('@/views/cases/CaseList.vue'),
        meta: { title: '商标案件' }
      },
      {
        path: 'copyright',
        name: 'CopyrightCases',
        component: () => import('@/views/cases/CaseList.vue'),
        meta: { title: '版权案件' }
      },
      {
        path: 'litigation',
        name: 'LitigationCases',
        component: () => import('@/views/cases/CaseList.vue'),
        meta: { title: '诉讼案件' }
      }
    ]
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/layout/index.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'reports',
        name: 'SearchReports',
        component: () => import('@/views/search/PatentSearch.vue'),
        meta: { title: '检索报告' }
      },
      {
        path: 'analysis',
        name: 'SearchAnalysis',
        component: () => import('@/views/search/PatentSearch.vue'),
        meta: { title: '分析报告' }
      }
    ]
  },
  {
    path: '/finance',
    name: 'Finance',
    component: () => import('@/layout/index.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'fees',
        name: 'Fees',
        component: () => import('@/views/finance/FinanceOverview.vue'),
        meta: { title: '费用管理' }
      },
      {
        path: 'billing',
        name: 'Billing',
        component: () => import('@/views/finance/FinanceOverview.vue'),
        meta: { title: '账单管理' }
      },
      {
        path: 'annuity',
        name: 'Annuity',
        component: () => import('@/views/finance/FinanceOverview.vue'),
        meta: { title: '年费管理' }
      }
    ]
  },
  {
    path: '/knowledge',
    name: 'Knowledge',
    component: () => import('@/layout/index.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'templates',
        name: 'Templates',
        component: () => import('@/views/knowledge/KnowledgeBase.vue'),
        meta: { title: '模板库' }
      },
      {
        path: 'training',
        name: 'Training',
        component: () => import('@/views/knowledge/KnowledgeBase.vue'),
        meta: { title: '培训考核' }
      }
    ]
  },
  {
    path: '/reports',
    name: 'Reports',
    component: () => import('@/layout/index.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'statistics',
        name: 'Statistics',
        component: () => import('@/views/reports/Reports.vue'),
        meta: { title: '统计报表' }
      },
      {
        path: 'performance',
        name: 'Performance',
        component: () => import('@/views/reports/Reports.vue'),
        meta: { title: '绩效分析' }
      }
    ]
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/layout/index.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/settings/Settings.vue'),
        meta: { title: '个人设置' }
      },
      {
        path: 'system',
        name: 'SystemSettings',
        component: () => import('@/views/settings/Settings.vue'),
        meta: { title: '系统设置' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/404.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  NProgress.start()
  
  const userStore = useUserStore()
  const isAuthenticated = userStore.isAuthenticated
  
  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && isAuthenticated) {
    next('/dashboard')
  } else {
    next()
  }
})

router.afterEach(() => {
  NProgress.done()
})

export default router